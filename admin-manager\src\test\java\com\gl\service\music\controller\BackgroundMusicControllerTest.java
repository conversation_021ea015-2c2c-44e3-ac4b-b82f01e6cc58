package com.gl.service.music.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.web.response.Result;
import com.gl.framework.web.response.ResultCode;
import com.gl.service.music.service.BackgroundMusicService;
import com.gl.service.music.vo.BackGroundMusicVo;
import com.gl.service.music.vo.dto.BackgroundMusicDto;
import com.gl.service.opus.entity.BackgroundMusicType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.test.context.ContextConfiguration;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * BackgroundMusicController单元测试类
 *
 * 测试覆盖范围：
 * 1. list方法 - GET /music - 背景音乐列表查询
 * 2. delete方法 - DELETE /music - 背景音乐删除
 * 3. add方法 - POST /music - 背景音乐添加
 * 4. findBackgroundMusicType方法 - GET /music/type - 背景音乐类型查询
 *
 * <AUTHOR> Generator
 */
@ExtendWith(SpringExtension.class)
@WebMvcTest(controllers = BackgroundMusicController.class, excludeAutoConfiguration = {
                org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class,
                org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration.class
}, useDefaultFilters = false)
@ContextConfiguration(classes = { BackgroundMusicController.class, BackgroundMusicControllerTest.TestConfig.class })
@DisplayName("BackgroundMusicController单元测试")
class BackgroundMusicControllerTest {

        @Autowired
        private MockMvc mockMvc;

        @MockBean
        private BackgroundMusicService backgroundMusicService;

        @Autowired
        private ObjectMapper objectMapper;

        private BackgroundMusicDto testDto;
        private BackGroundMusicVo testVo;
        private BackgroundMusicType testType;
        private Result successResult;
        private Result failResult;

        @BeforeEach
        void setUp() {
                setupTestData();
        }

        /**
         * 初始化测试数据
         */
        private void setupTestData() {
                // 初始化DTO
                testDto = new BackgroundMusicDto();
                testDto.setPageNumber(0);
                testDto.setPageSize(10);
                testDto.setSearchCondition("测试音乐");
                testDto.setShopId(1L);
                testDto.setBackgroundMusicId(1L);

                // 初始化VO
                testVo = new BackGroundMusicVo();
                testVo.setId(1L);
                testVo.setTypeId(1L);
                testVo.setName("测试背景音乐");
                testVo.setMusicUrl("test/music.wav");
                testVo.setShopId(1L);
                testVo.setMusicTime(120);
                testVo.setCreateTime(new Date());

                // 初始化类型
                testType = new BackgroundMusicType();
                testType.setId(1L);
                testType.setName("测试类型");

                // 初始化结果
                successResult = Result.success();
                Map<String, Object> data = new HashMap<>();
                data.put("total", 1);
                data.put("result", Arrays.asList(testVo));
                successResult.addData("total", 1);
                successResult.addData("result", Arrays.asList(testVo));

                failResult = Result.fail("操作失败");
        }

        // ==================== list方法测试 ====================

        @Test
        @DisplayName("测试list方法 - 正常查询返回数据")
        void testList_Success() throws Exception {
                // Given - 准备测试数据
                when(backgroundMusicService.list(any(BackgroundMusicDto.class))).thenReturn(successResult);

                // When & Then - 执行测试并验证结果
                mockMvc.perform(get("/music")
                                .param("pageNumber", "0")
                                .param("pageSize", "10")
                                .param("searchCondition", "测试音乐")
                                .param("shopId", "1")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()))
                                .andExpect(jsonPath("$.message").value("success"));
        }

        @Test
        @DisplayName("测试list方法 - 服务层返回失败")
        void testList_ServiceFail() throws Exception {
                // Given - 模拟服务层返回失败
                when(backgroundMusicService.list(any(BackgroundMusicDto.class))).thenReturn(failResult);

                // When & Then - 执行测试并验证结果
                mockMvc.perform(get("/music")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode()))
                                .andExpect(jsonPath("$.message").value("操作失败"));
        }

        // ==================== delete方法测试 ====================

        @Test
        @DisplayName("测试delete方法 - 正常删除成功")
        void testDelete_Success() throws Exception {
                // Given - 准备测试数据
                when(backgroundMusicService.delete(any(BackgroundMusicDto.class))).thenReturn(successResult);

                // When & Then - 执行测试并验证结果
                mockMvc.perform(delete("/music")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));
        }

        @Test
        @DisplayName("测试delete方法 - 请求体为空")
        void testDelete_EmptyBody() throws Exception {
                // When & Then - 执行测试并验证结果
                mockMvc.perform(delete("/music")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("{}"))
                                .andDo(print())
                                .andExpect(status().isOk());
        }

        @Test
        @DisplayName("测试delete方法 - 服务层返回失败")
        void testDelete_ServiceFail() throws Exception {
                // Given - 模拟服务层返回失败
                when(backgroundMusicService.delete(any(BackgroundMusicDto.class))).thenReturn(failResult);

                // When & Then - 执行测试并验证结果
                mockMvc.perform(delete("/music")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode()))
                                .andExpect(jsonPath("$.message").value("操作失败"));
        }

        // ==================== add方法测试 ====================

        @Test
        @DisplayName("测试add方法 - 正常添加成功")
        void testAdd_Success() throws Exception {
                // Given - 准备测试数据
                when(backgroundMusicService.add(any(BackGroundMusicVo.class))).thenReturn(successResult);

                // When & Then - 执行测试并验证结果
                mockMvc.perform(post("/music")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testVo)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));
        }

        @Test
        @DisplayName("测试add方法 - 请求体为空")
        void testAdd_EmptyBody() throws Exception {
                // When & Then - 执行测试并验证结果
                mockMvc.perform(post("/music")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("{}"))
                                .andDo(print())
                                .andExpect(status().isOk());
        }

        @Test
        @DisplayName("测试add方法 - 服务层返回失败")
        void testAdd_ServiceFail() throws Exception {
                // Given - 模拟服务层返回失败
                when(backgroundMusicService.add(any(BackGroundMusicVo.class))).thenReturn(failResult);

                // When & Then - 执行测试并验证结果
                mockMvc.perform(post("/music")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testVo)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode()))
                                .andExpect(jsonPath("$.message").value("操作失败"));
        }

        @Test
        @DisplayName("测试add方法 - 无效JSON格式")
        void testAdd_InvalidJson() throws Exception {
                // When & Then - 执行测试并验证结果
                mockMvc.perform(post("/music")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("invalid json"))
                                .andDo(print())
                                .andExpect(status().isBadRequest());
        }

        // ==================== findBackgroundMusicType方法测试 ====================

        @Test
        @DisplayName("测试findBackgroundMusicType方法 - 正常获取类型列表")
        void testFindBackgroundMusicType_Success() throws Exception {
                // Given - 准备测试数据
                Result typeResult = Result.success(Arrays.asList(testType));
                when(backgroundMusicService.findBackgroundMusicType()).thenReturn(typeResult);

                // When & Then - 执行测试并验证结果
                mockMvc.perform(get("/music/type")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()))
                                .andExpect(jsonPath("$.data").isArray())
                                .andExpect(jsonPath("$.data[0].id").value(1))
                                .andExpect(jsonPath("$.data[0].name").value("测试类型"));
        }

        @Test
        @DisplayName("测试findBackgroundMusicType方法 - 服务层返回失败")
        void testFindBackgroundMusicType_ServiceFail() throws Exception {
                // Given - 模拟服务层返回失败
                when(backgroundMusicService.findBackgroundMusicType()).thenReturn(failResult);

                // When & Then - 执行测试并验证结果
                mockMvc.perform(get("/music/type")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode()))
                                .andExpect(jsonPath("$.message").value("操作失败"));
        }

        @Test
        @DisplayName("测试findBackgroundMusicType方法 - 服务层抛出异常")
        void testFindBackgroundMusicType_ServiceException() throws Exception {
                // Given - 模拟服务层抛出异常
                when(backgroundMusicService.findBackgroundMusicType())
                                .thenThrow(new RuntimeException("数据库连接异常"));

                // When & Then - 执行测试并验证异常处理
                // 注意：在WebMvcTest中，异常可能被包装，所以我们检查是否抛出了异常
                try {
                        mockMvc.perform(get("/music/type")
                                        .contentType(MediaType.APPLICATION_JSON))
                                        .andDo(print());
                        // 如果没有抛出异常，测试失败
                        fail("Expected exception was not thrown");
                } catch (Exception e) {
                        // 验证异常类型和消息
                        assertTrue(e.getCause() instanceof RuntimeException);
                        assertTrue(e.getCause().getMessage().contains("数据库连接异常"));
                }
        }

        // ==================== 集成测试 ====================

        @Test
        @DisplayName("测试完整的CRUD流程")
        void testFullCrudFlow() throws Exception {
                // 1. 获取类型列表
                Result typeResult = Result.success(Arrays.asList(testType));
                when(backgroundMusicService.findBackgroundMusicType()).thenReturn(typeResult);

                mockMvc.perform(get("/music/type"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));

                // 2. 添加背景音乐
                when(backgroundMusicService.add(any(BackGroundMusicVo.class))).thenReturn(successResult);

                mockMvc.perform(post("/music")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testVo)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));

                // 3. 查询背景音乐列表
                when(backgroundMusicService.list(any(BackgroundMusicDto.class))).thenReturn(successResult);

                mockMvc.perform(get("/music"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));

                // 4. 删除背景音乐
                when(backgroundMusicService.delete(any(BackgroundMusicDto.class))).thenReturn(successResult);

                mockMvc.perform(delete("/music")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDto)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));
        }

        // ==================== 边界条件测试 ====================

        @Test
        @DisplayName("测试HTTP方法不匹配")
        void testWrongHttpMethod() throws Exception {
                // When & Then - 使用错误的HTTP方法
                mockMvc.perform(post("/music/type")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isMethodNotAllowed());
        }

        @Test
        @DisplayName("测试不支持的Content-Type")
        void testUnsupportedContentType() throws Exception {
                // When & Then - 使用不支持的Content-Type
                mockMvc.perform(post("/music")
                                .contentType(MediaType.TEXT_PLAIN)
                                .content("plain text content"))
                                .andDo(print())
                                .andExpect(status().isUnsupportedMediaType());
        }

        @TestConfiguration
        static class TestConfig {
                // 测试配置类，用于解决多个SpringBootConfiguration的问题
        }
}
