package com.gl.service.installationPackage.controller;

import com.gl.config.BaseControllerTest;
import com.gl.framework.web.response.Result;
import com.gl.framework.web.response.ResultCode;
import com.gl.service.installationPackage.service.InstallationPackageService;
import com.gl.service.installationPackage.vo.installationPackage.dto.InstallationPackageAddDto;
import com.gl.service.installationPackage.vo.installationPackage.dto.InstallationPackageDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * InstallationPackageController单元测试类
 * 测试安装包管理控制器的所有公共方法，包括正面和负面场景
 */
@DisplayName("安装包控制器单元测试")
class InstallationPackageControllerTest extends BaseControllerTest {

    @MockBean
    private InstallationPackageService installationPackageService;

    private InstallationPackageDto mockDto;
    private InstallationPackageAddDto mockAddDto;

    @BeforeEach
    void setUp() {
        // 重置mock对象
        reset(installationPackageService);

        // 配置权限服务模拟
        allowPermission("dub:installationPackage:list");
        allowPermission("dub:installationPackage:add");
        allowPermission("dub:installationPackage:delete");
        denyPermission("other:permission");

        // 初始化测试数据
        mockDto = new InstallationPackageDto();
        mockDto.setSearchCondition("test");

        mockAddDto = new InstallationPackageAddDto();
        mockAddDto.setVersionName("v1.0.0");
        mockAddDto.setRemark("测试更新说明");
        mockAddDto.setPackageUrl("http://example.com/package.apk");
    }

    // ==================== list方法测试 ====================

    @Test
    @DisplayName("查询安装包列表 - 成功场景，有权限")
    @WithMockUser(authorities = "dub:installationPackage:list")
    void testList_WithPermission_Success() throws Exception {
        // Given
        Result mockResult = createSuccessResultWithData();
        when(installationPackageService.list(any(InstallationPackageDto.class), eq(1))).thenReturn(mockResult);

        // When & Then
        mockMvc.perform(get("/installationPackage").param("searchCondition", "test").contentType(MediaType.APPLICATION_JSON)).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode())).andExpect(jsonPath("$.message").value("success")).andExpect(jsonPath("$.data.total").value(2)).andExpect(jsonPath("$.data.result").isArray());

        verify(installationPackageService, times(1)).list(any(InstallationPackageDto.class), eq(1));
    }

    @Test
    @DisplayName("查询安装包列表 - 成功场景，无搜索条件")
    @WithMockUser(authorities = "dub:installationPackage:list")
    void testList_WithoutSearchCondition_Success() throws Exception {
        // Given
        Result mockResult = createEmptyResult();
        when(installationPackageService.list(any(InstallationPackageDto.class), eq(1))).thenReturn(mockResult);

        // When & Then
        mockMvc.perform(get("/installationPackage").contentType(MediaType.APPLICATION_JSON)).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode())).andExpect(jsonPath("$.message").value("success")).andExpect(jsonPath("$.data.total").value(0));

        verify(installationPackageService, times(1)).list(any(InstallationPackageDto.class), eq(1));
    }

    @Test
    @DisplayName("查询安装包列表 - 无权限场景")
    @WithMockUser(authorities = "other:permission")
    void testList_WithoutPermission_Forbidden() throws Exception {
        // Given - 配置权限拒绝
        denyPermission("dub:installationPackage:list");

        // When & Then
        mockMvc.perform(get("/installationPackage").contentType(MediaType.APPLICATION_JSON)).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(40003)); // Access is denied

        verify(installationPackageService, never()).list(any(InstallationPackageDto.class), anyInt());
    }

    @Test
    @DisplayName("查询安装包列表 - 未认证场景")
    void testList_Unauthenticated_Unauthorized() throws Exception {
        // 注意：在测试环境中，由于安全配置可能被简化，未认证的请求可能不会返回401
        // 这个测试验证的是在没有认证的情况下的行为
        // When & Then
        mockMvc.perform(get("/installationPackage").contentType(MediaType.APPLICATION_JSON)).andDo(print()).andExpect(status().isOk()); // 在测试环境中可能返回200而不是401

        // 由于Spring Security在测试环境中的行为，未认证用户的请求可能被拦截，服务不会被调用
        verify(installationPackageService, never()).list(any(InstallationPackageDto.class), anyInt());
    }

    @Test
    @DisplayName("查询安装包列表 - 服务层异常场景")
    @WithMockUser(authorities = "dub:installationPackage:list")
    void testList_ServiceException() throws Exception {
        // Given
        when(installationPackageService.list(any(InstallationPackageDto.class), eq(1))).thenThrow(new RuntimeException("服务异常"));

        // When & Then - GlobalExceptionHandler会捕获异常并返回200状态码
        mockMvc.perform(get("/installationPackage").param("searchCondition", "test").contentType(MediaType.APPLICATION_JSON)).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode())).andExpect(jsonPath("$.message").value("服务异常"));

        verify(installationPackageService, times(1)).list(any(InstallationPackageDto.class), eq(1));
    }

    // ==================== addOrUpdate方法测试 ====================

    @Test
    @DisplayName("新增/修改安装包 - 成功场景，有权限")
    @WithMockUser(authorities = "dub:installationPackage:add")
    void testAddOrUpdate_WithPermission_Success() throws Exception {
        // Given
        Result mockResult = Result.success();
        when(installationPackageService.addOrUpdate(any(InstallationPackageAddDto.class))).thenReturn(mockResult);

        // When & Then
        mockMvc.perform(post("/installationPackage").with(csrf()).contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(mockAddDto))).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode())).andExpect(jsonPath("$.message").value("success"));

        verify(installationPackageService, times(1)).addOrUpdate(any(InstallationPackageAddDto.class));
    }

    @Test
    @DisplayName("新增/修改安装包 - 修改场景")
    @WithMockUser(authorities = "dub:installationPackage:add")
    void testAddOrUpdate_UpdateMode_Success() throws Exception {
        // Given
        mockAddDto.setId(1L);
        Result mockResult = Result.success();
        when(installationPackageService.addOrUpdate(any(InstallationPackageAddDto.class))).thenReturn(mockResult);

        // When & Then
        mockMvc.perform(post("/installationPackage").with(csrf()).contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(mockAddDto))).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode())).andExpect(jsonPath("$.message").value("success"));

        verify(installationPackageService, times(1)).addOrUpdate(any(InstallationPackageAddDto.class));
    }

    @Test
    @DisplayName("新增/修改安装包 - 服务层返回失败结果")
    @WithMockUser(authorities = "dub:installationPackage:add")
    void testAddOrUpdate_ServiceReturnsFail() throws Exception {
        // Given
        Result mockResult = Result.fail("版本号不能为空");
        when(installationPackageService.addOrUpdate(any(InstallationPackageAddDto.class))).thenReturn(mockResult);

        // When & Then
        mockMvc.perform(post("/installationPackage").with(csrf()).contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(mockAddDto))).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode())).andExpect(jsonPath("$.message").value("版本号不能为空"));

        verify(installationPackageService, times(1)).addOrUpdate(any(InstallationPackageAddDto.class));
    }

    @Test
    @DisplayName("新增/修改安装包 - 无权限场景")
    @WithMockUser(authorities = "other:permission")
    void testAddOrUpdate_WithoutPermission_Forbidden() throws Exception {
        // Given - 配置权限拒绝
        denyPermission("dub:installationPackage:add");

        // When & Then
        mockMvc.perform(post("/installationPackage").with(csrf()).contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(mockAddDto))).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(40003)); // Access is denied

        verify(installationPackageService, never()).addOrUpdate(any(InstallationPackageAddDto.class));
    }

    @Test
    @DisplayName("新增/修改安装包 - 未认证场景")
    void testAddOrUpdate_Unauthenticated_Unauthorized() throws Exception {
        // 注意：在测试环境中，由于安全配置可能被简化，未认证的请求可能不会返回401
        // When & Then
        mockMvc.perform(post("/installationPackage").with(csrf()).contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(mockAddDto))).andDo(print()).andExpect(status().isOk()); // 在测试环境中可能返回200而不是401

        // 由于Spring Security在测试环境中的行为，未认证用户的请求可能被拦截，服务不会被调用
        verify(installationPackageService, never()).addOrUpdate(any(InstallationPackageAddDto.class));
    }

    @Test
    @DisplayName("新增/修改安装包 - 缺少CSRF令牌场景")
    @WithMockUser(authorities = "dub:installationPackage:add")
    void testAddOrUpdate_MissingCsrfToken_Forbidden() throws Exception {
        // Given
        Result mockResult = Result.success();
        when(installationPackageService.addOrUpdate(any(InstallationPackageAddDto.class))).thenReturn(mockResult);

        // 注意：在应用中CSRF被禁用，所以这个测试实际上会成功
        // When & Then
        mockMvc.perform(post("/installationPackage").contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(mockAddDto))).andDo(print()).andExpect(status().isOk()) // CSRF被禁用，所以返回200
                .andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));

        verify(installationPackageService, times(1)).addOrUpdate(any(InstallationPackageAddDto.class));
    }

    @Test
    @DisplayName("新增/修改安装包 - 无效JSON格式场景")
    @WithMockUser(authorities = "dub:installationPackage:add")
    void testAddOrUpdate_InvalidJson_BadRequest() throws Exception {
        // When & Then - GlobalExceptionHandler会捕获JSON解析异常并返回200状态码
        mockMvc.perform(post("/installationPackage").with(csrf()).contentType(MediaType.APPLICATION_JSON).content("invalid json")).andDo(print()).andExpect(status().isOk()) // GlobalExceptionHandler返回200
                .andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode()));

        verify(installationPackageService, never()).addOrUpdate(any(InstallationPackageAddDto.class));
    }

    @Test
    @DisplayName("新增/修改安装包 - 服务层异常场景")
    @WithMockUser(authorities = "dub:installationPackage:add")
    void testAddOrUpdate_ServiceException() throws Exception {
        // Given
        when(installationPackageService.addOrUpdate(any(InstallationPackageAddDto.class))).thenThrow(new RuntimeException("服务异常"));

        // When & Then - GlobalExceptionHandler会捕获异常并返回200状态码
        mockMvc.perform(post("/installationPackage").with(csrf()).contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(mockAddDto))).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode())).andExpect(jsonPath("$.message").value("服务异常"));

        verify(installationPackageService, times(1)).addOrUpdate(any(InstallationPackageAddDto.class));
    }

    // ==================== delete方法测试 ====================

    @Test
    @DisplayName("删除安装包 - 成功场景，有权限")
    @WithMockUser(authorities = "dub:installationPackage:delete")
    void testDelete_WithPermission_Success() throws Exception {
        // Given
        mockAddDto.setIds(Arrays.asList(1L, 2L, 3L));
        Result mockResult = Result.success();
        when(installationPackageService.delete(any(InstallationPackageAddDto.class))).thenReturn(mockResult);

        // When & Then
        mockMvc.perform(delete("/installationPackage").with(csrf()).contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(mockAddDto))).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode())).andExpect(jsonPath("$.message").value("success"));

        verify(installationPackageService, times(1)).delete(any(InstallationPackageAddDto.class));
    }

    @Test
    @DisplayName("删除安装包 - 单个ID成功场景")
    @WithMockUser(authorities = "dub:installationPackage:delete")
    void testDelete_SingleId_Success() throws Exception {
        // Given
        mockAddDto.setIds(Arrays.asList(1L));
        Result mockResult = Result.success();
        when(installationPackageService.delete(any(InstallationPackageAddDto.class))).thenReturn(mockResult);

        // When & Then
        mockMvc.perform(delete("/installationPackage").with(csrf()).contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(mockAddDto))).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode())).andExpect(jsonPath("$.message").value("success"));

        verify(installationPackageService, times(1)).delete(any(InstallationPackageAddDto.class));
    }

    @Test
    @DisplayName("删除安装包 - 服务层返回失败结果")
    @WithMockUser(authorities = "dub:installationPackage:delete")
    void testDelete_ServiceReturnsFail() throws Exception {
        // Given
        mockAddDto.setIds(Arrays.asList(1L));
        Result mockResult = Result.fail("安装包id不能为空");
        when(installationPackageService.delete(any(InstallationPackageAddDto.class))).thenReturn(mockResult);

        // When & Then
        mockMvc.perform(delete("/installationPackage").with(csrf()).contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(mockAddDto))).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode())).andExpect(jsonPath("$.message").value("安装包id不能为空"));

        verify(installationPackageService, times(1)).delete(any(InstallationPackageAddDto.class));
    }

    @Test
    @DisplayName("删除安装包 - 无权限场景")
    @WithMockUser(authorities = "other:permission")
    void testDelete_WithoutPermission_Forbidden() throws Exception {
        // Given
        mockAddDto.setIds(Arrays.asList(1L));
        denyPermission("dub:installationPackage:delete");

        // When & Then
        mockMvc.perform(delete("/installationPackage").with(csrf()).contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(mockAddDto))).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(40003)); // Access is denied

        verify(installationPackageService, never()).delete(any(InstallationPackageAddDto.class));
    }

    @Test
    @DisplayName("删除安装包 - 未认证场景")
    void testDelete_Unauthenticated_Unauthorized() throws Exception {
        // Given
        mockAddDto.setIds(Arrays.asList(1L));

        // 注意：在测试环境中，由于安全配置可能被简化，未认证的请求可能不会返回401
        // When & Then
        mockMvc.perform(delete("/installationPackage").with(csrf()).contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(mockAddDto))).andDo(print()).andExpect(status().isOk()); // 在测试环境中可能返回200而不是401

        // 由于Spring Security在测试环境中的行为，未认证用户的请求可能被拦截，服务不会被调用
        verify(installationPackageService, never()).delete(any(InstallationPackageAddDto.class));
    }

    @Test
    @DisplayName("删除安装包 - 缺少CSRF令牌场景")
    @WithMockUser(authorities = "dub:installationPackage:delete")
    void testDelete_MissingCsrfToken_Forbidden() throws Exception {
        // Given
        mockAddDto.setIds(Arrays.asList(1L));
        Result mockResult = Result.success();
        when(installationPackageService.delete(any(InstallationPackageAddDto.class))).thenReturn(mockResult);

        // When & Then - 在测试环境中CSRF可能被禁用，所以测试会成功
        mockMvc.perform(delete("/installationPackage").contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(mockAddDto))).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));

        verify(installationPackageService, times(1)).delete(any(InstallationPackageAddDto.class));
    }

    @Test
    @DisplayName("删除安装包 - 无效JSON格式场景")
    @WithMockUser(authorities = "dub:installationPackage:delete")
    void testDelete_InvalidJson_BadRequest() throws Exception {
        // When & Then - GlobalExceptionHandler会捕获JSON解析异常并返回200状态码
        mockMvc.perform(delete("/installationPackage").with(csrf()).contentType(MediaType.APPLICATION_JSON).content("invalid json")).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode()));

        verify(installationPackageService, never()).delete(any(InstallationPackageAddDto.class));
    }

    @Test
    @DisplayName("删除安装包 - 服务层异常场景")
    @WithMockUser(authorities = "dub:installationPackage:delete")
    void testDelete_ServiceException() throws Exception {
        // Given
        mockAddDto.setIds(Arrays.asList(1L));
        when(installationPackageService.delete(any(InstallationPackageAddDto.class))).thenThrow(new RuntimeException("服务异常"));

        // When & Then - GlobalExceptionHandler会捕获异常并返回200状态码
        mockMvc.perform(delete("/installationPackage").with(csrf()).contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(mockAddDto))).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode())).andExpect(jsonPath("$.message").value("服务异常"));

        verify(installationPackageService, times(1)).delete(any(InstallationPackageAddDto.class));
    }

    // ==================== 边缘案例和集成测试 ====================

    @Test
    @DisplayName("查询安装包列表 - 带有特殊字符的搜索条件")
    @WithMockUser(authorities = "dub:installationPackage:list")
    void testList_WithSpecialCharacters_Success() throws Exception {
        // Given
        Result mockResult = createEmptyResult();
        when(installationPackageService.list(any(InstallationPackageDto.class), eq(1))).thenReturn(mockResult);

        // When & Then
        mockMvc.perform(get("/installationPackage").param("searchCondition", "test@#$%^&*()").contentType(MediaType.APPLICATION_JSON)).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));

        verify(installationPackageService, times(1)).list(any(InstallationPackageDto.class), eq(1));
    }

    @Test
    @DisplayName("新增/修改安装包 - 空请求体场景")
    @WithMockUser(authorities = "dub:installationPackage:add")
    void testAddOrUpdate_EmptyBody_BadRequest() throws Exception {
        // When & Then - GlobalExceptionHandler会捕获JSON解析异常并返回200状态码
        mockMvc.perform(post("/installationPackage").with(csrf()).contentType(MediaType.APPLICATION_JSON).content("")).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode()));

        verify(installationPackageService, never()).addOrUpdate(any(InstallationPackageAddDto.class));
    }

    @Test
    @DisplayName("删除安装包 - 空请求体场景")
    @WithMockUser(authorities = "dub:installationPackage:delete")
    void testDelete_EmptyBody_BadRequest() throws Exception {
        // When & Then - GlobalExceptionHandler会捕获JSON解析异常并返回200状态码
        mockMvc.perform(delete("/installationPackage").with(csrf()).contentType(MediaType.APPLICATION_JSON).content("")).andDo(print()).andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON)).andExpect(jsonPath("$.code").value(ResultCode.FAIL.getCode()));

        verify(installationPackageService, never()).delete(any(InstallationPackageAddDto.class));
    }

    // 创建辅助方法
    private Result createSuccessResultWithData() {
        Result result = Result.success();
        Map<String, Object> data = new HashMap<>();
        data.put("total", 2L);
        data.put("result", Arrays.asList("item1", "item2"));
        result.setData(data);
        return result;
    }

    private Result createEmptyResult() {
        Result result = Result.success();
        Map<String, Object> data = new HashMap<>();
        data.put("total", 0L);
        data.put("result", null);
        result.setData(data);
        return result;
    }
}
