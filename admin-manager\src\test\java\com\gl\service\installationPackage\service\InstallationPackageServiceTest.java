package com.gl.service.installationPackage.service;

import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.response.Result;
import com.gl.framework.web.response.ResultCode;
import com.gl.service.installationPackage.entity.InstallationPackage;
import com.gl.service.installationPackage.repository.InstallationPackageRepository;
import com.gl.service.installationPackage.vo.installationPackage.InstallationPackageVo;
import com.gl.service.installationPackage.vo.installationPackage.dto.InstallationPackageAddDto;
import com.gl.service.installationPackage.vo.installationPackage.dto.InstallationPackageDto;
import com.gl.system.vo.SysUserVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * InstallationPackageService单元测试类
 * 测试安装包管理服务的所有公共方法，包括正面和负面场景
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("安装包服务单元测试")
class InstallationPackageServiceTest {

    @Mock
    private InstallationPackageRepository installationPackageRepository;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private InstallationPackageService installationPackageService;

    private InstallationPackageDto mockDto;
    private InstallationPackageAddDto mockAddDto;
    private InstallationPackage mockEntity;
    private SysUserVo mockUser;
    private LoginUser mockLoginUser;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockDto = new InstallationPackageDto();
        mockDto.setSearchCondition("test");
        mockDto.setPageNumber(0);
        mockDto.setPageSize(10);

        mockAddDto = new InstallationPackageAddDto();
        mockAddDto.setVersionName("v1.0.0");
        mockAddDto.setRemark("测试更新说明");
        mockAddDto.setPackageUrl("http://example.com/package.apk");

        mockEntity = new InstallationPackage();
        mockEntity.setId(1L);
        mockEntity.setVersionName("v1.0.0");
        mockEntity.setRemark("测试更新说明");
        mockEntity.setPackageUrl("http://example.com/package.apk");
        mockEntity.setCreateTime(new Date());
        mockEntity.setCreateUserId(1L);

        mockUser = new SysUserVo();
        mockUser.setId(1L);
        mockUser.setUserName("testUser");

        mockLoginUser = new LoginUser();
        mockLoginUser.setUser(mockUser);
    }

    // ==================== list方法测试 ====================

    @Test
    @DisplayName("查询安装包列表 - 成功场景，有搜索条件")
    void testList_WithSearchCondition_Success() {
        // Given
        List<InstallationPackageVo> mockVoList = Arrays.asList(
                createMockInstallationPackageVo(1L, "v1.0.0", "测试说明1"),
                createMockInstallationPackageVo(2L, "v2.0.0", "测试说明2"));

        // Mock count query - 使用更宽松的匹配
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(2L);

        // Mock data query - 使用更宽松的匹配
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(mockVoList);

        // When
        Result result = installationPackageService.list(mockDto, 1);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应返回成功状态码");
        assertEquals("success", result.getMessage(), "应返回成功消息");

        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(2L, data.get("total"), "总数应为2");
        assertEquals(mockVoList, data.get("result"), "结果列表应匹配");

        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("查询安装包列表 - 成功场景，无搜索条件")
    void testList_WithoutSearchCondition_Success() {
        // Given
        InstallationPackageDto dtoWithoutSearch = new InstallationPackageDto();
        dtoWithoutSearch.setPageNumber(0);
        dtoWithoutSearch.setPageSize(10);

        List<InstallationPackageVo> mockVoList = Arrays.asList(
                createMockInstallationPackageVo(1L, "v1.0.0", "测试说明1"));

        // Mock count query - 使用宽松匹配
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(1L);

        // Mock data query - 使用宽松匹配
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(mockVoList);

        // When
        Result result = installationPackageService.list(dtoWithoutSearch, 1);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应返回成功状态码");

        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(1L, data.get("total"), "总数应为1");
        assertEquals(mockVoList, data.get("result"), "结果列表应匹配");
    }

    @Test
    @DisplayName("查询安装包列表 - 空结果场景")
    void testList_EmptyResult_Success() {
        // Given
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(0L);

        // When
        Result result = installationPackageService.list(mockDto, 1);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应返回成功状态码");

        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(0L, data.get("total"), "总数应为0");
        assertNull(data.get("result"), "结果应为null");

        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate, never()).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("查询安装包列表 - null参数场景")
    void testList_NullDto_Success() {
        // Given - 使用宽松匹配
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(1L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(Arrays.asList(createMockInstallationPackageVo(1L, "v1.0.0", "测试说明")));

        // When
        Result result = installationPackageService.list(null, 1);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应返回成功状态码");
    }

    @Test
    @DisplayName("查询安装包列表 - 导出模式场景")
    void testList_ExportMode_Success() {
        // Given
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(1L);
        // 使用宽松匹配
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(Arrays.asList(createMockInstallationPackageVo(1L, "v1.0.0", "测试说明")));

        // When
        Result result = installationPackageService.list(mockDto, 0); // exportType != 1

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应返回成功状态码");

        // 验证不会添加LIMIT和OFFSET子句
        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    // ==================== addOrUpdate方法测试 ====================

    @Test
    @DisplayName("新增安装包 - 成功场景")
    void testAddOrUpdate_Add_Success() {
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            // Given
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);
            when(installationPackageRepository.save(any(InstallationPackage.class))).thenReturn(mockEntity);

            // When
            Result result = installationPackageService.addOrUpdate(mockAddDto);

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应返回成功状态码");
            assertEquals("success", result.getMessage(), "应返回成功消息");

            verify(installationPackageRepository, times(1)).save(any(InstallationPackage.class));
            verify(installationPackageRepository, never()).findById(anyLong());
        }
    }

    @Test
    @DisplayName("修改安装包 - 成功场景")
    void testAddOrUpdate_Update_Success() {
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            // Given
            mockAddDto.setId(1L);
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);
            when(installationPackageRepository.findById(1L)).thenReturn(Optional.of(mockEntity));
            when(installationPackageRepository.save(any(InstallationPackage.class))).thenReturn(mockEntity);

            // When
            Result result = installationPackageService.addOrUpdate(mockAddDto);

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应返回成功状态码");
            assertEquals("success", result.getMessage(), "应返回成功消息");

            verify(installationPackageRepository, times(1)).findById(1L);
            verify(installationPackageRepository, times(1)).save(any(InstallationPackage.class));
        }
    }

    @Test
    @DisplayName("修改安装包 - 记录不存在场景")
    void testAddOrUpdate_Update_NotFound() {
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            // Given
            mockAddDto.setId(999L);
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);
            when(installationPackageRepository.findById(999L)).thenReturn(Optional.empty());

            // When
            Result result = installationPackageService.addOrUpdate(mockAddDto);

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应返回成功状态码");

            verify(installationPackageRepository, times(1)).findById(999L);
            verify(installationPackageRepository, never()).save(any(InstallationPackage.class));
        }
    }

    @Test
    @DisplayName("新增/修改安装包 - 参数为null场景")
    void testAddOrUpdate_NullParameter_Fail() {
        // When
        Result result = installationPackageService.addOrUpdate(null);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应返回失败状态码");
        assertEquals("数据不能为空", result.getMessage(), "应返回正确的错误消息");

        verify(installationPackageRepository, never()).save(any(InstallationPackage.class));
        verify(installationPackageRepository, never()).findById(anyLong());
    }

    @Test
    @DisplayName("新增/修改安装包 - 版本号为空场景")
    void testAddOrUpdate_EmptyVersionName_Fail() {
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            // Given
            mockAddDto.setVersionName("");
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When
            Result result = installationPackageService.addOrUpdate(mockAddDto);

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应返回失败状态码");
            assertEquals("版本号不能为空", result.getMessage(), "应返回正确的错误消息");

            verify(installationPackageRepository, never()).save(any(InstallationPackage.class));
        }
    }

    @Test
    @DisplayName("新增/修改安装包 - 版本号为null场景")
    void testAddOrUpdate_NullVersionName_Fail() {
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            // Given
            mockAddDto.setVersionName(null);
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When
            Result result = installationPackageService.addOrUpdate(mockAddDto);

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应返回失败状态码");
            assertEquals("版本号不能为空", result.getMessage(), "应返回正确的错误消息");

            verify(installationPackageRepository, never()).save(any(InstallationPackage.class));
        }
    }

    @Test
    @DisplayName("新增/修改安装包 - 更新说明为空场景")
    void testAddOrUpdate_EmptyRemark_Fail() {
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            // Given
            mockAddDto.setRemark("");
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When
            Result result = installationPackageService.addOrUpdate(mockAddDto);

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应返回失败状态码");
            assertEquals("更新说明不能为空", result.getMessage(), "应返回正确的错误消息");

            verify(installationPackageRepository, never()).save(any(InstallationPackage.class));
        }
    }

    @Test
    @DisplayName("新增/修改安装包 - 更新说明为null场景")
    void testAddOrUpdate_NullRemark_Fail() {
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            // Given
            mockAddDto.setRemark(null);
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When
            Result result = installationPackageService.addOrUpdate(mockAddDto);

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应返回失败状态码");
            assertEquals("更新说明不能为空", result.getMessage(), "应返回正确的错误消息");

            verify(installationPackageRepository, never()).save(any(InstallationPackage.class));
        }
    }

    @Test
    @DisplayName("新增/修改安装包 - 更新包URL为空场景")
    void testAddOrUpdate_EmptyPackageUrl_Fail() {
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            // Given
            mockAddDto.setPackageUrl("");
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When
            Result result = installationPackageService.addOrUpdate(mockAddDto);

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应返回失败状态码");
            assertEquals("更新包不能为空", result.getMessage(), "应返回正确的错误消息");

            verify(installationPackageRepository, never()).save(any(InstallationPackage.class));
        }
    }

    @Test
    @DisplayName("新增/修改安装包 - 更新包URL为null场景")
    void testAddOrUpdate_NullPackageUrl_Fail() {
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            // Given
            mockAddDto.setPackageUrl(null);
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When
            Result result = installationPackageService.addOrUpdate(mockAddDto);

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应返回失败状态码");
            assertEquals("更新包不能为空", result.getMessage(), "应返回正确的错误消息");

            verify(installationPackageRepository, never()).save(any(InstallationPackage.class));
        }
    }

    // ==================== delete方法测试 ====================

    @Test
    @DisplayName("删除安装包 - 成功场景")
    void testDelete_Success() {
        // Given
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        mockAddDto.setIds(ids);
        doNothing().when(installationPackageRepository).deleteById(anyLong());

        // When
        Result result = installationPackageService.delete(mockAddDto);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应返回成功状态码");
        assertEquals("success", result.getMessage(), "应返回成功消息");

        verify(installationPackageRepository, times(3)).deleteById(anyLong());
        verify(installationPackageRepository, times(1)).deleteById(1L);
        verify(installationPackageRepository, times(1)).deleteById(2L);
        verify(installationPackageRepository, times(1)).deleteById(3L);
    }

    @Test
    @DisplayName("删除安装包 - 单个ID成功场景")
    void testDelete_SingleId_Success() {
        // Given
        List<Long> ids = Arrays.asList(1L);
        mockAddDto.setIds(ids);
        doNothing().when(installationPackageRepository).deleteById(1L);

        // When
        Result result = installationPackageService.delete(mockAddDto);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应返回成功状态码");
        assertEquals("success", result.getMessage(), "应返回成功消息");

        verify(installationPackageRepository, times(1)).deleteById(1L);
    }

    @Test
    @DisplayName("删除安装包 - 参数为null场景")
    void testDelete_NullParameter_Fail() {
        // When
        Result result = installationPackageService.delete(null);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应返回失败状态码");
        assertEquals("数据不能为空", result.getMessage(), "应返回正确的错误消息");

        verify(installationPackageRepository, never()).deleteById(anyLong());
    }

    @Test
    @DisplayName("删除安装包 - ID列表为空场景")
    void testDelete_EmptyIds_Fail() {
        // Given
        mockAddDto.setIds(new ArrayList<>());

        // When
        Result result = installationPackageService.delete(mockAddDto);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应返回失败状态码");
        assertEquals("安装包id不能为空", result.getMessage(), "应返回正确的错误消息");

        verify(installationPackageRepository, never()).deleteById(anyLong());
    }

    @Test
    @DisplayName("删除安装包 - ID列表为null场景")
    void testDelete_NullIds_Fail() {
        // Given
        mockAddDto.setIds(null);

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            installationPackageService.delete(mockAddDto);
        }, "应抛出NullPointerException异常");

        verify(installationPackageRepository, never()).deleteById(anyLong());
    }

    // ==================== 边缘案例和异常处理测试 ====================

    @Test
    @DisplayName("查询安装包列表 - 数据库异常场景")
    void testList_DatabaseException() {
        // Given
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            installationPackageService.list(mockDto, 1);
        }, "应抛出数据库异常");

        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate, never()).query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class));
    }

    @Test
    @DisplayName("新增安装包 - 数据库保存异常场景")
    void testAddOrUpdate_Add_DatabaseException() {
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            // Given
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);
            when(installationPackageRepository.save(any(InstallationPackage.class)))
                    .thenThrow(new RuntimeException("数据库保存异常"));

            // When & Then
            assertThrows(RuntimeException.class, () -> {
                installationPackageService.addOrUpdate(mockAddDto);
            }, "应抛出数据库异常");

            verify(installationPackageRepository, times(1)).save(any(InstallationPackage.class));
        }
    }

    @Test
    @DisplayName("删除安装包 - 数据库删除异常场景")
    void testDelete_DatabaseException() {
        // Given
        List<Long> ids = Arrays.asList(1L);
        mockAddDto.setIds(ids);
        doThrow(new RuntimeException("数据库删除异常"))
                .when(installationPackageRepository).deleteById(1L);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            installationPackageService.delete(mockAddDto);
        }, "应抛出数据库异常");

        verify(installationPackageRepository, times(1)).deleteById(1L);
    }

    // 创建辅助方法
    private InstallationPackageVo createMockInstallationPackageVo(Long id, String versionName, String remark) {
        InstallationPackageVo vo = new InstallationPackageVo();
        vo.setId(id);
        vo.setVersionName(versionName);
        vo.setRemark(remark);
        vo.setPackageUrl("http://example.com/package.apk");
        vo.setUpdateDeviceNum(0);
        vo.setCreateTime(new Date());
        return vo;
    }
}
