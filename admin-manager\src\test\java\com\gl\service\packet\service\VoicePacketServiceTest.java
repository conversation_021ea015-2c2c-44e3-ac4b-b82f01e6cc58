package com.gl.service.packet.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gl.framework.config.oss.OssConfig;
import com.gl.framework.web.response.Result;
import com.gl.service.device.vo.DeviceVo;
import com.gl.service.opus.repository.VoicePacketRepository;
import com.gl.service.opus.repository.VoiceWorkRepository;
import com.gl.service.oss.service.OSSService;
import com.gl.service.packet.vo.VoicePacketVo;
import com.gl.service.packet.vo.dto.VoicePacketDto;
import com.gl.util.GetShopRefUtil;
import com.gl.util.ZipUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * VoicePacketService单元测试类
 * 测试语音包管理服务的所有业务逻辑
 * 
 * @author: 测试开发
 * @date: 2025-07-15
 * @version: 1.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("语音包服务测试类")
class VoicePacketServiceTest {

    @Mock(lenient = true)
    private JdbcTemplate jdbcTemplate;

    @Mock
    private VoicePacketRepository voicePacketRepository;

    @Mock
    private VoiceWorkRepository voiceWorkRepository;

    @Mock
    private OSSService ossService;

    @Mock
    private OssConfig ossConfig;

    @Mock(lenient = true)
    private GetShopRefUtil shopRefUtil;

    @InjectMocks
    private VoicePacketService voicePacketService;

    private VoicePacketDto testDto;
    private List<VoicePacketVo> testVoicePacketVos;
    private List<DeviceVo> testDeviceVos;

    @BeforeEach
    @DisplayName("初始化测试数据")
    void setUp() {
        // 初始化测试DTO
        testDto = new VoicePacketDto();
        testDto.setId(1L);
        testDto.setShopId(100L);
        testDto.setSearchCondition("测试语音包");
        testDto.setPageNumber(0);
        testDto.setPageSize(10);

        // 初始化测试语音包VO列表
        testVoicePacketVos = createTestVoicePacketVos();

        // 初始化测试设备VO列表
        testDeviceVos = createTestDeviceVos();
    }

    /**
     * 创建测试用的语音包VO列表
     */
    private List<VoicePacketVo> createTestVoicePacketVos() {
        List<VoicePacketVo> vos = new ArrayList<>();

        VoicePacketVo vo1 = new VoicePacketVo();
        vo1.setId(1L);
        vo1.setName("测试语音包1");
        vo1.setVoiceTime(30);
        vo1.setFileUrl("test/voice1.mp3");
        vo1.setNickname("用户1");
        vo1.setPhone("13800138001");
        vo1.setCreateTime(new Date());
        vo1.setShopName("测试门店1");
        vos.add(vo1);

        VoicePacketVo vo2 = new VoicePacketVo();
        vo2.setId(2L);
        vo2.setName("测试语音包2");
        vo2.setVoiceTime(45);
        vo2.setFileUrl("test/voice2.mp3");
        vo2.setNickname("用户2");
        vo2.setPhone("13800138002");
        vo2.setCreateTime(new Date());
        vo2.setShopName("测试门店2");
        vos.add(vo2);

        return vos;
    }

    /**
     * 创建测试用的设备VO列表
     */
    private List<DeviceVo> createTestDeviceVos() {
        List<DeviceVo> vos = new ArrayList<>();

        DeviceVo vo1 = new DeviceVo();
        vo1.setId(1L);
        vo1.setName("测试设备1");
        vo1.setSn("SN001");
        vos.add(vo1);

        DeviceVo vo2 = new DeviceVo();
        vo2.setId(2L);
        vo2.setName("测试设备2");
        vo2.setSn("SN002");
        vos.add(vo2);

        return vos;
    }

    // ==================== list方法测试 ====================

    @Test
    @DisplayName("测试list方法 - 正常查询场景")
    void testList_正常查询场景() {
        // Given - 准备测试数据
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(100L, 200L));

        // Mock count查询 - 使用具体的参数匹配
        doReturn(2L).when(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());

        // Mock list查询
        doReturn(testVoicePacketVos).when(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any());

        // When - 执行测试方法
        Result result = voicePacketService.list(testDto, 1);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "数据不应为空");
        assertEquals(2L, data.get("total"), "总数应为2");
        assertEquals(testVoicePacketVos, data.get("result"), "结果列表应匹配");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }

    @Test
    @DisplayName("测试list方法 - 微信过滤场景")
    void testList_微信过滤场景() {
        // Given - 准备测试数据
        when(shopRefUtil.isNeedWxFilter()).thenReturn(true);

        // When - 执行测试方法
        Result result = voicePacketService.list(testDto, 1);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "数据不应为空");
        assertEquals(0, data.get("total"), "总数应为0");
        assertTrue(((List<?>) data.get("result")).isEmpty(), "结果列表应为空");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil, never()).getShopRef();
        verify(jdbcTemplate, never()).queryForObject(anyString(), eq(Long.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试list方法 - 查询结果为空场景")
    void testList_查询结果为空场景() {
        // Given - 准备测试数据
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(100L, 200L));
        doReturn(0L).when(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());

        // When - 执行测试方法
        Result result = voicePacketService.list(testDto, 1);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "数据不应为空");
        assertEquals(0, data.get("total"), "总数应为0");
        assertTrue(((List<?>) data.get("result")).isEmpty(), "结果列表应为空");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());
        verify(jdbcTemplate, never()).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }

    @Test
    @DisplayName("测试list方法 - DTO为空场景")
    void testList_DTO为空场景() {
        // Given - 准备测试数据
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(100L, 200L));
        doReturn(2L).when(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());
        doReturn(testVoicePacketVos).when(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any());

        // When & Then - 执行测试方法，期望抛出断言错误
        // 因为在exportType=1时，代码中有assert dto != null;
        assertThrows(AssertionError.class, () -> {
            voicePacketService.list(null, 1);
        });

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());
        // 注意：由于断言错误，query方法不会被调用
    }

    @Test
    @DisplayName("测试list方法 - 导出类型为2场景")
    void testList_导出类型为2场景() {
        // Given - 准备测试数据
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(100L, 200L));
        doReturn(2L).when(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());
        doReturn(testVoicePacketVos).when(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any());

        // When - 执行测试方法
        Result result = voicePacketService.list(testDto, 2);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "数据不应为空");
        assertEquals(2L, data.get("total"), "总数应为2");
        assertEquals(testVoicePacketVos, data.get("result"), "结果列表应匹配");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }

    @Test
    @DisplayName("测试list方法 - 门店引用为空场景")
    void testList_门店引用为空场景() {
        // Given - 准备测试数据
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Collections.emptyList());
        doReturn(2L).when(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());
        doReturn(testVoicePacketVos).when(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any());

        // When - 执行测试方法
        Result result = voicePacketService.list(testDto, 1);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "数据不应为空");
        assertEquals(2L, data.get("total"), "总数应为2");
        assertEquals(testVoicePacketVos, data.get("result"), "结果列表应匹配");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }

    // ==================== delete方法测试 ====================

    @Test
    @DisplayName("测试delete方法 - 正常删除场景")
    void testDelete_正常删除场景() {
        // Given - 准备测试数据
        VoicePacketDto deleteDto = new VoicePacketDto();
        deleteDto.setId(1L);

        // When - 执行测试方法
        Result result = voicePacketService.delete(deleteDto);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");
        assertEquals("success", result.getMessage(), "返回消息应为success");

        // 验证方法调用
        verify(voicePacketRepository).deleteById(1L);
        verify(voiceWorkRepository).updateDelStatusByVoiceId(1L);
    }

    @Test
    @DisplayName("测试delete方法 - DTO为空场景")
    void testDelete_DTO为空场景() {
        // When - 执行测试方法
        Result result = voicePacketService.delete(null);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10001, result.getCode(), "返回码应为失败");
        assertEquals("数据不能为空", result.getMessage(), "返回消息应为数据不能为空");

        // 验证方法未被调用
        verify(voicePacketRepository, never()).deleteById(anyLong());
        verify(voiceWorkRepository, never()).updateDelStatusByVoiceId(anyLong());
    }

    @Test
    @DisplayName("测试delete方法 - ID为空场景")
    void testDelete_ID为空场景() {
        // Given - 准备测试数据
        VoicePacketDto deleteDto = new VoicePacketDto();
        deleteDto.setId(null);

        // When - 执行测试方法
        Result result = voicePacketService.delete(deleteDto);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10001, result.getCode(), "返回码应为失败");
        assertEquals("语音包id不能为空", result.getMessage(), "返回消息应为语音包id不能为空");

        // 验证方法未被调用
        verify(voicePacketRepository, never()).deleteById(anyLong());
        verify(voiceWorkRepository, never()).updateDelStatusByVoiceId(anyLong());
    }

    @Test
    @DisplayName("测试delete方法 - 数据库异常场景")
    void testDelete_数据库异常场景() {
        // Given - 准备测试数据
        VoicePacketDto deleteDto = new VoicePacketDto();
        deleteDto.setId(1L);

        doThrow(new RuntimeException("数据库连接异常")).when(voicePacketRepository).deleteById(1L);

        // When & Then - 执行测试方法并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            voicePacketService.delete(deleteDto);
        });

        assertEquals("数据库连接异常", exception.getMessage(), "异常消息应匹配");

        // 验证方法调用
        verify(voicePacketRepository).deleteById(1L);
        verify(voiceWorkRepository, never()).updateDelStatusByVoiceId(anyLong());
    }

    // ==================== detail方法测试 ====================

    @Test
    @DisplayName("测试detail方法 - 正常查询场景")
    void testDetail_正常查询场景() {
        // Given - 准备测试数据
        Long voicePacketId = 1L;
        doReturn(testDeviceVos).when(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class),
                eq(voicePacketId));

        // When - 执行测试方法
        Result result = voicePacketService.detail(voicePacketId);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");
        assertEquals(testDeviceVos, result.getData(), "返回数据应匹配");

        // 验证方法调用
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), eq(voicePacketId));
    }

    @Test
    @DisplayName("测试detail方法 - ID为空场景")
    void testDetail_ID为空场景() {
        // When - 执行测试方法
        Result result = voicePacketService.detail(null);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10001, result.getCode(), "返回码应为失败");
        assertEquals("语音包id不能为空", result.getMessage(), "返回消息应为语音包id不能为空");

        // 验证方法未被调用
        verify(jdbcTemplate, never()).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }

    @Test
    @DisplayName("测试detail方法 - 查询结果为空场景")
    void testDetail_查询结果为空场景() {
        // Given - 准备测试数据
        Long voicePacketId = 999L;
        doReturn(Collections.emptyList()).when(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class),
                eq(voicePacketId));

        // When - 执行测试方法
        Result result = voicePacketService.detail(voicePacketId);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");
        assertTrue(((List<?>) result.getData()).isEmpty(), "返回数据应为空列表");

        // 验证方法调用
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), eq(voicePacketId));
    }

    @Test
    @DisplayName("测试detail方法 - 数据库异常场景")
    void testDetail_数据库异常场景() {
        // Given - 准备测试数据
        Long voicePacketId = 1L;
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(voicePacketId)))
                .thenThrow(new RuntimeException("数据库查询异常"));

        // When & Then - 执行测试方法并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            voicePacketService.detail(voicePacketId);
        });

        assertEquals("数据库查询异常", exception.getMessage(), "异常消息应匹配");

        // 验证方法调用
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), eq(voicePacketId));
    }

    // ==================== plistDownLoad方法测试 ====================

    @Test
    @DisplayName("测试plistDownLoad方法 - 正常下载场景")
    void testPlistDownLoad_正常下载场景() throws IOException {
        // Given - 准备测试数据
        HttpServletResponse response = mock(HttpServletResponse.class);
        ServletOutputStream outputStream = mock(ServletOutputStream.class);

        when(response.getOutputStream()).thenReturn(outputStream);
        when(ossConfig.getDownFilePacketdir()).thenReturn("/tmp/test/");

        // Mock list方法返回结果
        Result listResult = Result.success();
        Map<String, Object> resultData = new HashMap<>();
        resultData.put("total", 2L);
        resultData.put("result", testVoicePacketVos);
        listResult.setData(resultData);

        // Mock VoicePacketService的list方法
        VoicePacketService spyService = spy(voicePacketService);
        doReturn(listResult).when(spyService).list(any(VoicePacketDto.class), eq(2));

        // Mock OSS服务
        File mockFile1 = mock(File.class);
        File mockFile2 = mock(File.class);
        when(ossService.getObjectFilePacket(isNull(), eq("test/voice1.mp3"), anyLong(), eq("测试语音包1")))
                .thenReturn(mockFile1);
        when(ossService.getObjectFilePacket(isNull(), eq("test/voice2.mp3"), anyLong(), eq("测试语音包2")))
                .thenReturn(mockFile2);

        // Mock静态方法
        try (MockedStatic<ZipUtils> zipUtilsMock = mockStatic(ZipUtils.class)) {
            // When - 执行测试方法
            spyService.plistDownLoad(testDto, response);

            // Then - 验证结果
            verify(response).setContentType("application/octet-stream");
            verify(response).setHeader(eq("Content-disposition"), contains("attachment; filename="));
            verify(spyService).list(testDto, 2);
            verify(ossService).getObjectFilePacket(isNull(), eq("test/voice1.mp3"), anyLong(), eq("测试语音包1"));
            verify(ossService).getObjectFilePacket(isNull(), eq("test/voice2.mp3"), anyLong(), eq("测试语音包2"));

            zipUtilsMock.verify(() -> ZipUtils.toZip(anyList(), eq(outputStream)));
        }
    }

    @Test
    @DisplayName("测试plistDownLoad方法 - 无语音包数据场景")
    void testPlistDownLoad_无语音包数据场景() throws IOException {
        // Given - 准备测试数据
        HttpServletResponse response = mock(HttpServletResponse.class);
        ServletOutputStream outputStream = mock(ServletOutputStream.class);

        when(response.getOutputStream()).thenReturn(outputStream);
        when(ossConfig.getDownFilePacketdir()).thenReturn("/tmp/test/");

        // Mock list方法返回空结果
        Result listResult = Result.success();
        Map<String, Object> resultData = new HashMap<>();
        resultData.put("total", 0L);
        resultData.put("result", Collections.emptyList());
        listResult.setData(resultData);

        VoicePacketService spyService = spy(voicePacketService);
        doReturn(listResult).when(spyService).list(any(VoicePacketDto.class), eq(2));

        // Mock静态方法
        try (MockedStatic<ZipUtils> zipUtilsMock = mockStatic(ZipUtils.class)) {
            // When - 执行测试方法
            spyService.plistDownLoad(testDto, response);

            // Then - 验证结果
            verify(response).setContentType("application/octet-stream");
            verify(response).setHeader(eq("Content-disposition"), contains("attachment; filename="));
            verify(spyService).list(testDto, 2);
            verify(ossService, never()).getObjectFilePacket(any(), any(), anyLong(), any());

            zipUtilsMock.verify(() -> ZipUtils.toZip(eq(Collections.emptyList()), eq(outputStream)));
        }
    }

    @Test
    @DisplayName("测试plistDownLoad方法 - OSS下载失败场景")
    void testPlistDownLoad_OSS下载失败场景() throws IOException {
        // Given - 准备测试数据
        HttpServletResponse response = mock(HttpServletResponse.class);
        ServletOutputStream outputStream = mock(ServletOutputStream.class);

        when(response.getOutputStream()).thenReturn(outputStream);
        when(ossConfig.getDownFilePacketdir()).thenReturn("/tmp/test/");

        // Mock list方法返回结果
        Result listResult = Result.success();
        Map<String, Object> resultData = new HashMap<>();
        resultData.put("total", 1L);
        resultData.put("result", Arrays.asList(testVoicePacketVos.get(0)));
        listResult.setData(resultData);

        VoicePacketService spyService = spy(voicePacketService);
        doReturn(listResult).when(spyService).list(any(VoicePacketDto.class), eq(2));

        // Mock OSS服务返回null（下载失败）
        when(ossService.getObjectFilePacket(isNull(), eq("test/voice1.mp3"), anyLong(), eq("测试语音包1")))
                .thenReturn(null);

        // Mock静态方法
        try (MockedStatic<ZipUtils> zipUtilsMock = mockStatic(ZipUtils.class)) {
            // When - 执行测试方法
            spyService.plistDownLoad(testDto, response);

            // Then - 验证结果
            verify(response).setContentType("application/octet-stream");
            verify(response).setHeader(eq("Content-disposition"), contains("attachment; filename="));
            verify(spyService).list(testDto, 2);
            verify(ossService).getObjectFilePacket(isNull(), eq("test/voice1.mp3"), anyLong(), eq("测试语音包1"));

            // 应该传递空列表给ZipUtils
            zipUtilsMock.verify(() -> ZipUtils.toZip(eq(Collections.emptyList()), eq(outputStream)));
        }
    }

    @Test
    @DisplayName("测试plistDownLoad方法 - 响应流异常场景")
    void testPlistDownLoad_响应流异常场景() throws IOException {
        // Given - 准备测试数据
        HttpServletResponse response = mock(HttpServletResponse.class);

        when(response.getOutputStream()).thenThrow(new IOException("输出流异常"));
        when(ossConfig.getDownFilePacketdir()).thenReturn("/tmp/test/");

        // Mock list方法返回结果
        Result listResult = Result.success();
        Map<String, Object> resultData = new HashMap<>();
        resultData.put("total", 1L);
        resultData.put("result", Arrays.asList(testVoicePacketVos.get(0)));
        listResult.setData(resultData);

        VoicePacketService spyService = spy(voicePacketService);
        doReturn(listResult).when(spyService).list(any(VoicePacketDto.class), eq(2));

        // When & Then - 执行测试方法并验证异常
        IOException exception = assertThrows(IOException.class, () -> {
            spyService.plistDownLoad(testDto, response);
        });

        assertEquals("输出流异常", exception.getMessage(), "异常消息应匹配");

        // 验证方法调用
        verify(response).setContentType("application/octet-stream");
        verify(response).setHeader(eq("Content-disposition"), contains("attachment; filename="));
        verify(spyService).list(testDto, 2);
    }

    @Test
    @DisplayName("测试plistDownLoad方法 - 文件URL为空场景")
    void testPlistDownLoad_文件URL为空场景() throws IOException {
        // Given - 准备测试数据
        HttpServletResponse response = mock(HttpServletResponse.class);
        ServletOutputStream outputStream = mock(ServletOutputStream.class);

        when(response.getOutputStream()).thenReturn(outputStream);
        when(ossConfig.getDownFilePacketdir()).thenReturn("/tmp/test/");

        // 创建文件URL为空的语音包
        VoicePacketVo voWithNullUrl = new VoicePacketVo();
        voWithNullUrl.setId(3L);
        voWithNullUrl.setName("无URL语音包");
        voWithNullUrl.setFileUrl(null);

        Result listResult = Result.success();
        Map<String, Object> resultData = new HashMap<>();
        resultData.put("total", 1L);
        resultData.put("result", Arrays.asList(voWithNullUrl));
        listResult.setData(resultData);

        VoicePacketService spyService = spy(voicePacketService);
        doReturn(listResult).when(spyService).list(any(VoicePacketDto.class), eq(2));

        // Mock静态方法
        try (MockedStatic<ZipUtils> zipUtilsMock = mockStatic(ZipUtils.class)) {
            // When - 执行测试方法
            spyService.plistDownLoad(testDto, response);

            // Then - 验证结果
            verify(response).setContentType("application/octet-stream");
            verify(response).setHeader(eq("Content-disposition"), contains("attachment; filename="));
            verify(spyService).list(testDto, 2);
            verify(ossService, never()).getObjectFilePacket(any(), any(), anyLong(), any());

            // 应该传递空列表给ZipUtils（因为没有有效的文件URL）
            zipUtilsMock.verify(() -> ZipUtils.toZip(eq(Collections.emptyList()), eq(outputStream)));
        }
    }

    // ==================== 大量数据性能测试 ====================

    @Test
    @DisplayName("测试list方法 - 大量数据性能测试")
    void testList_大量数据性能测试() {
        // Given - 准备大量测试数据
        List<VoicePacketVo> largeDataSet = createLargeVoicePacketDataSet(10000);

        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(100L, 200L, 300L, 400L, 500L));
        doReturn((long) largeDataSet.size()).when(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());
        doReturn(largeDataSet).when(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any());

        // When - 执行测试方法并测量性能
        long startTime = System.currentTimeMillis();
        Result result = voicePacketService.list(testDto, 1);
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证结果和性能
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "数据不应为空");
        assertEquals((long) largeDataSet.size(), data.get("total"), "总数应匹配");
        assertEquals(largeDataSet, data.get("result"), "结果列表应匹配");

        // 性能断言 - 处理10000条数据应在1秒内完成
        assertTrue(executionTime < 1000,
                String.format("处理%d条数据耗时%dms，超过预期的1000ms", largeDataSet.size(), executionTime));

        System.out.println(String.format("大量数据性能测试 - 处理%d条数据耗时: %dms", largeDataSet.size(), executionTime));

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }

    @Test
    @DisplayName("测试detail方法 - 大量设备数据性能测试")
    void testDetail_大量设备数据性能测试() {
        // Given - 准备大量设备测试数据
        List<DeviceVo> largeDeviceDataSet = createLargeDeviceDataSet(5000);
        Long voicePacketId = 1L;

        doReturn(largeDeviceDataSet).when(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class),
                eq(voicePacketId));

        // When - 执行测试方法并测量性能
        long startTime = System.currentTimeMillis();
        Result result = voicePacketService.detail(voicePacketId);
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证结果和性能
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");
        assertEquals(largeDeviceDataSet, result.getData(), "返回数据应匹配");

        // 性能断言 - 处理5000条设备数据应在500ms内完成
        assertTrue(executionTime < 500,
                String.format("处理%d条设备数据耗时%dms，超过预期的500ms", largeDeviceDataSet.size(), executionTime));

        System.out.println(String.format("大量设备数据性能测试 - 处理%d条数据耗时: %dms", largeDeviceDataSet.size(), executionTime));

        // 验证方法调用
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), eq(voicePacketId));
    }

    @Test
    @DisplayName("测试delete方法 - 批量删除性能测试")
    void testDelete_批量删除性能测试() {
        // Given - 准备批量删除测试数据
        List<Long> idsToDelete = Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L);

        // When - 执行批量删除并测量性能
        long startTime = System.currentTimeMillis();

        for (Long id : idsToDelete) {
            VoicePacketDto deleteDto = new VoicePacketDto();
            deleteDto.setId(id);
            Result result = voicePacketService.delete(deleteDto);

            // 验证每次删除都成功
            assertNotNull(result, "返回结果不应为空");
            assertEquals(10000, result.getCode(), "返回码应为成功");
        }

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证性能
        // 批量删除10条记录应在200ms内完成
        assertTrue(executionTime < 200,
                String.format("批量删除%d条记录耗时%dms，超过预期的200ms", idsToDelete.size(), executionTime));

        System.out.println(String.format("批量删除性能测试 - 删除%d条记录耗时: %dms", idsToDelete.size(), executionTime));

        // 验证方法调用次数
        verify(voicePacketRepository, times(idsToDelete.size())).deleteById(anyLong());
        verify(voiceWorkRepository, times(idsToDelete.size())).updateDelStatusByVoiceId(anyLong());
    }

    /**
     * 创建大量语音包测试数据
     */
    private List<VoicePacketVo> createLargeVoicePacketDataSet(int size) {
        List<VoicePacketVo> largeDataSet = new ArrayList<>();

        for (int i = 1; i <= size; i++) {
            VoicePacketVo vo = new VoicePacketVo();
            vo.setId((long) i);
            vo.setName("测试语音包" + i);
            vo.setVoiceTime(30 + (i % 60)); // 30-90秒随机时长
            vo.setFileUrl("test/voice" + i + ".mp3");
            vo.setNickname("用户" + i);
            vo.setPhone("1380013800" + String.format("%02d", i % 100));
            vo.setCreateTime(new Date(System.currentTimeMillis() - (i * 1000L)));
            vo.setShopName("测试门店" + (i % 100 + 1));
            largeDataSet.add(vo);
        }

        return largeDataSet;
    }

    /**
     * 创建大量设备测试数据
     */
    private List<DeviceVo> createLargeDeviceDataSet(int size) {
        List<DeviceVo> largeDataSet = new ArrayList<>();

        for (int i = 1; i <= size; i++) {
            DeviceVo vo = new DeviceVo();
            vo.setId((long) i);
            vo.setName("测试设备" + i);
            vo.setSn("SN" + String.format("%06d", i));
            vo.setUserId((long) (i % 1000 + 1));
            vo.setVolume(50 + (i % 50)); // 50-100音量
            vo.setStatus(i % 2); // 0离线 1在线
            vo.setBindStatus(1); // 已绑定
            vo.setUseStatus(1); // 使用中
            vo.setDelStatus(0); // 未删除
            largeDataSet.add(vo);
        }

        return largeDataSet;
    }

    // ==================== 边界条件和异常场景测试 ====================

    @Test
    @DisplayName("测试list方法 - 搜索条件包含特殊字符")
    void testList_搜索条件包含特殊字符() {
        // Given - 准备包含特殊字符的搜索条件
        VoicePacketDto specialDto = new VoicePacketDto();
        specialDto.setSearchCondition("测试'语音\"包%_");
        specialDto.setPageNumber(0);
        specialDto.setPageSize(10);

        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(100L));
        doReturn(1L).when(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());
        doReturn(Arrays.asList(testVoicePacketVos.get(0))).when(jdbcTemplate).query(anyString(),
                any(BeanPropertyRowMapper.class), any());

        // When - 执行测试方法
        Result result = voicePacketService.list(specialDto, 1);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "数据不应为空");
        assertEquals(1L, data.get("total"), "总数应为1");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }

    @Test
    @DisplayName("测试list方法 - 极大页码和页大小")
    void testList_极大页码和页大小() {
        // Given - 准备极大的分页参数
        VoicePacketDto largePageDto = new VoicePacketDto();
        largePageDto.setPageNumber(Integer.MAX_VALUE);
        largePageDto.setPageSize(Integer.MAX_VALUE);

        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(100L));
        doReturn(0L).when(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());

        // When - 执行测试方法
        Result result = voicePacketService.list(largePageDto, 1);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(10000, result.getCode(), "返回码应为成功");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "数据不应为空");
        assertEquals(0, data.get("total"), "总数应为0");
        assertTrue(((List<?>) data.get("result")).isEmpty(), "结果列表应为空");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());
        verify(jdbcTemplate, never()).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }
}
