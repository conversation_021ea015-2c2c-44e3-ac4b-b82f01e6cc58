package com.gl.service.packet.controller;

import com.gl.framework.security.service.PermissionService;
import com.gl.framework.security.service.TokenService;
import com.gl.framework.security.LoginUser;
import com.gl.system.vo.SysUserVo;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.mockito.Mockito;

import javax.servlet.http.HttpServletRequest;
import java.util.HashSet;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 语音包控制器测试配置类
 * 用于提供测试环境中需要的Mock Bean
 */
@TestConfiguration
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class VoicePacketControllerTestConfig {

    @Bean
    @Primary
    public StringRedisTemplate stringRedisTemplate() {
        return Mockito.mock(StringRedisTemplate.class);
    }

    @Bean
    @Primary
    @SuppressWarnings("unchecked")
    public RedisTemplate<String, Object> redisTemplate() {
        return Mockito.mock(RedisTemplate.class);
    }

    @Bean
    @Primary
    public JedisConnectionFactory jedisConnectionFactory() {
        return Mockito.mock(JedisConnectionFactory.class);
    }

    @Bean("ps")
    @Primary
    public PermissionService permissionService() {
        // 返回一个可以在测试中重新配置的Mock对象
        return Mockito.mock(PermissionService.class);
    }

    @Bean
    @Primary
    public TokenService tokenService() {
        TokenService mockService = Mockito.mock(TokenService.class);

        // 创建模拟的登录用户
        SysUserVo mockUser = new SysUserVo();
        mockUser.setId(1L);
        mockUser.setLoginName("testuser");
        mockUser.setUserName("测试用户");

        Set<String> permissions = new HashSet<>();
        permissions.add("packet:packet:list");
        permissions.add("packet:packet:detail");
        permissions.add("packet:packet:delete");
        permissions.add("packet:packet:zipdown");

        LoginUser mockLoginUser = new LoginUser(mockUser, null, null, null, permissions);

        // 模拟 getLoginUser 方法返回登录用户
        when(mockService.getLoginUser(any(HttpServletRequest.class))).thenReturn(mockLoginUser);

        return mockService;
    }
}
