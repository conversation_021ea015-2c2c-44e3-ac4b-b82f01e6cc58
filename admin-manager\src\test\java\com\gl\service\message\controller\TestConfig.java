package com.gl.service.message.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.security.service.PermissionService;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * 测试配置类
 * 为BaseServiceMessageController测试提供必要的Bean配置
 */
@TestConfiguration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class TestConfig extends WebSecurityConfigurerAdapter {

    @Bean
    @Primary
    public PermissionService permissionService() {
        PermissionService mockService = mock(PermissionService.class);
        when(mockService.hasPermi("message:message:list")).thenReturn(true);
        when(mockService.hasPermi("message:message:delete")).thenReturn(true);
        when(mockService.hasPermi("other:permission")).thenReturn(false);
        return mockService;
    }

    @Bean
    public ObjectMapper objectMapper() {
        return new ObjectMapper();
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.csrf().disable()
            .authorizeRequests()
            .anyRequest().permitAll();
    }
}
