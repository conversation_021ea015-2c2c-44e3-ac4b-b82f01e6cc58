package com.gl.service.template.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.web.response.Result;
import com.gl.service.template.service.TemplateService;
import com.gl.service.template.vo.TemplateVo;
import com.gl.service.template.vo.dto.TemplateDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.context.TestPropertySource;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.context.annotation.Import;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * TemplateController单元测试类
 * 测试模板管理控制器的所有REST端点
 *
 * <AUTHOR>
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = TemplateTestApplication.class)
@AutoConfigureMockMvc
@WebAppConfiguration
@Import(TemplateTestConfiguration.class)
@TestPropertySource(properties = {
        "token.expireTime=30",
        "token.secret=testSecret",
        "token.header=Authorization"
})
@DisplayName("模板控制器单元测试")
class TemplateControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private org.springframework.context.ApplicationContext applicationContext;

    @MockBean
    private TemplateService templateService;

    @MockBean
    private com.gl.system.service.SysMenuService sysMenuService;

    @MockBean
    private com.gl.framework.security.service.SysLoginService sysLoginService;

    @MockBean
    private com.gl.system.service.SysConfigService sysConfigService;

    @MockBean
    private com.gl.system.service.SysUserService sysUserService;

    @MockBean
    private com.gl.framework.common.util.redis.RedisUtils redisUtils;

    @MockBean
    private com.gl.redis.RedisService redisService;

    @MockBean
    private org.springframework.data.redis.core.StringRedisTemplate stringRedisTemplate;

    @MockBean
    private org.springframework.data.redis.core.RedisTemplate<String, Object> redisTemplate;

    @MockBean
    private org.springframework.data.redis.connection.jedis.JedisConnectionFactory jedisConnectionFactory;

    @MockBean
    private com.gl.framework.security.service.TokenService tokenService;

    @MockBean
    private com.gl.framework.security.service.PermissionService permissionService;

    private ObjectMapper objectMapper;
    private TemplateDto mockTemplateDto;
    private TemplateVo mockTemplateVo;
    private Result mockSuccessResult;
    private Result mockFailResult;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();

        // 初始化模拟DTO数据
        mockTemplateDto = new TemplateDto();
        mockTemplateDto.setShopId(100L);
        mockTemplateDto.setTemplateTypeId(1L);
        mockTemplateDto.setSearchCondition("测试");
        mockTemplateDto.setIds(Arrays.asList(1L, 2L, 3L));

        // 初始化模拟VO数据
        mockTemplateVo = new TemplateVo();
        mockTemplateVo.setId(1L);
        mockTemplateVo.setTemplateTypeId(1L);
        mockTemplateVo.setTitle("测试模板");
        mockTemplateVo.setContent("测试内容");
        mockTemplateVo.setShopId(100L);
        mockTemplateVo.setCreateTime(new Date());

        // 初始化模拟结果数据
        mockSuccessResult = Result.success();
        Map<String, Object> data = new HashMap<>();
        data.put("total", 1);
        data.put("result", Arrays.asList(mockTemplateVo));
        mockSuccessResult.addData("total", 1);
        mockSuccessResult.addData("result", Arrays.asList(mockTemplateVo));

        mockFailResult = Result.fail("操作失败");
    }

    @Test
    @DisplayName("测试Spring上下文中是否存在TemplateController")
    void testTemplateControllerExists() {
        // 验证TemplateController Bean是否存在
        assertThat(applicationContext.containsBean("templateController")).isTrue();

        // 获取TemplateController Bean
        Object templateController = applicationContext.getBean("templateController");
        assertThat(templateController).isNotNull();
        assertThat(templateController).isInstanceOf(com.gl.service.template.controller.TemplateController.class);
    }

    @Test
    @DisplayName("测试请求映射是否正确注册")
    void testRequestMappings() {
        // 获取RequestMappingHandlerMapping Bean
        org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping handlerMapping = applicationContext
                .getBean(org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.class);

        // 获取所有的HandlerMethod
        java.util.Map<org.springframework.web.servlet.mvc.method.RequestMappingInfo, org.springframework.web.method.HandlerMethod> handlerMethods = handlerMapping
                .getHandlerMethods();

        // 打印所有映射信息用于调试
        System.out.println("=== 所有请求映射 ===");
        handlerMethods.forEach((info, method) -> {
            System.out.println("映射: " + info + " -> " + method);
        });

        // 检查是否存在/template的GET映射
        boolean hasTemplateGetMapping = handlerMethods.keySet().stream()
                .anyMatch(info -> {
                    java.util.Set<String> patterns = info.getPatternsCondition().getPatterns();
                    java.util.Set<org.springframework.web.bind.annotation.RequestMethod> methods = info
                            .getMethodsCondition().getMethods();
                    return patterns.contains("/template")
                            && methods.contains(org.springframework.web.bind.annotation.RequestMethod.GET);
                });

        assertThat(hasTemplateGetMapping).isTrue();
    }

    @Test
    @DisplayName("测试获取模板列表 - 成功获取")
    @WithMockUser(authorities = "template:template:list")
    void testList_Success() throws Exception {
        // Given
        when(templateService.list(any(TemplateDto.class), eq(1))).thenReturn(mockSuccessResult);

        // When & Then
        String responseContent = mockMvc.perform(get("/template")
                .param("shopId", "100")
                .param("templateTypeId", "1")
                .param("searchCondition", "测试")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();

        // 检查响应体是否为空
        if (responseContent == null || responseContent.trim().isEmpty()) {
            throw new AssertionError("响应体为空，控制器方法可能没有被调用");
        }

        // 验证JSON响应
        mockMvc.perform(get("/template")
                .param("shopId", "100")
                .param("templateTypeId", "1")
                .param("searchCondition", "测试")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.total").value(1));

        verify(templateService, times(1)).list(any(TemplateDto.class), eq(1));
    }

    @Test
    @DisplayName("测试获取模板列表 - 无权限访问")
    @WithMockUser(authorities = "other:permission")
    void testList_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(get("/template")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(templateService, never()).list(any(TemplateDto.class), anyInt());
    }

    @Test
    @DisplayName("测试获取模板列表 - 未认证用户")
    void testList_Unauthenticated() throws Exception {
        // When & Then
        mockMvc.perform(get("/template")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isUnauthorized());

        verify(templateService, never()).list(any(TemplateDto.class), anyInt());
    }

    @Test
    @DisplayName("测试导出模板列表 - 成功导出")
    @WithMockUser(authorities = "template:template:export")
    void testExportList_Success() throws Exception {
        // Given
        doNothing().when(templateService).exportList(any(TemplateDto.class), any(HttpServletResponse.class));

        // When & Then
        mockMvc.perform(post("/template/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateDto)))
                .andDo(print())
                .andExpect(status().isOk());

        verify(templateService, times(1)).exportList(any(TemplateDto.class), any(HttpServletResponse.class));
    }

    @Test
    @DisplayName("测试导出模板列表 - 处理IOException异常")
    @WithMockUser(authorities = "template:template:export")
    void testExportList_IOException() throws Exception {
        // Given
        doThrow(new IOException("导出异常")).when(templateService)
                .exportList(any(TemplateDto.class), any(HttpServletResponse.class));

        // When & Then
        mockMvc.perform(post("/template/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateDto)))
                .andDo(print())
                .andExpect(status().isInternalServerError());

        verify(templateService, times(1)).exportList(any(TemplateDto.class), any(HttpServletResponse.class));
    }

    @Test
    @DisplayName("测试导出模板列表 - 无权限访问")
    @WithMockUser(authorities = "other:permission")
    void testExportList_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(post("/template/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateDto)))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(templateService, never()).exportList(any(TemplateDto.class), any(HttpServletResponse.class));
    }

    @Test
    @DisplayName("测试新增和修改模板 - 成功新增")
    @WithMockUser(authorities = "template:template:addorupdate")
    void testAddAndUpdate_AddSuccess() throws Exception {
        // Given
        mockTemplateVo.setId(null); // 新增操作
        when(templateService.addAndUpdate(any(TemplateVo.class))).thenReturn(mockSuccessResult);

        // When & Then
        mockMvc.perform(post("/template")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(templateService, times(1)).addAndUpdate(any(TemplateVo.class));
    }

    @Test
    @DisplayName("测试新增和修改模板 - 成功修改")
    @WithMockUser(authorities = "template:template:addorupdate")
    void testAddAndUpdate_UpdateSuccess() throws Exception {
        // Given
        when(templateService.addAndUpdate(any(TemplateVo.class))).thenReturn(mockSuccessResult);

        // When & Then
        mockMvc.perform(post("/template")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(templateService, times(1)).addAndUpdate(any(TemplateVo.class));
    }

    @Test
    @DisplayName("测试新增和修改模板 - 业务逻辑失败")
    @WithMockUser(authorities = "template:template:addorupdate")
    void testAddAndUpdate_BusinessFail() throws Exception {
        // Given
        when(templateService.addAndUpdate(any(TemplateVo.class))).thenReturn(mockFailResult);

        // When & Then
        mockMvc.perform(post("/template")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("操作失败"));

        verify(templateService, times(1)).addAndUpdate(any(TemplateVo.class));
    }

    @Test
    @DisplayName("测试新增和修改模板 - 无权限访问")
    @WithMockUser(authorities = "other:permission")
    void testAddAndUpdate_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(post("/template")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateVo)))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(templateService, never()).addAndUpdate(any(TemplateVo.class));
    }

    @Test
    @DisplayName("测试新增和修改模板 - 请求体格式错误")
    @WithMockUser(authorities = "template:template:addorupdate")
    void testAddAndUpdate_InvalidRequestBody() throws Exception {
        // When & Then
        mockMvc.perform(post("/template")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content("invalid json"))
                .andDo(print())
                .andExpect(status().isBadRequest());

        verify(templateService, never()).addAndUpdate(any(TemplateVo.class));
    }

    @Test
    @DisplayName("测试删除模板 - 成功删除")
    @WithMockUser(authorities = "template:template:delete")
    void testDelete_Success() throws Exception {
        // Given
        when(templateService.delete(any(TemplateDto.class))).thenReturn(mockSuccessResult);

        // When & Then
        mockMvc.perform(delete("/template")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(templateService, times(1)).delete(any(TemplateDto.class));
    }

    @Test
    @DisplayName("测试删除模板 - 业务逻辑失败")
    @WithMockUser(authorities = "template:template:delete")
    void testDelete_BusinessFail() throws Exception {
        // Given
        when(templateService.delete(any(TemplateDto.class))).thenReturn(mockFailResult);

        // When & Then
        mockMvc.perform(delete("/template")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("操作失败"));

        verify(templateService, times(1)).delete(any(TemplateDto.class));
    }

    @Test
    @DisplayName("测试删除模板 - 无权限访问")
    @WithMockUser(authorities = "other:permission")
    void testDelete_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(delete("/template")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateDto)))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(templateService, never()).delete(any(TemplateDto.class));
    }

    @Test
    @DisplayName("测试删除模板 - 请求体格式错误")
    @WithMockUser(authorities = "template:template:delete")
    void testDelete_InvalidRequestBody() throws Exception {
        // When & Then
        mockMvc.perform(delete("/template")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content("invalid json"))
                .andDo(print())
                .andExpect(status().isBadRequest());

        verify(templateService, never()).delete(any(TemplateDto.class));
    }

    @Test
    @DisplayName("测试获取模板类型下拉框列表 - 成功获取")
    @WithMockUser
    void testFindTemplateType_Success() throws Exception {
        // Given
        when(templateService.findTemplateType()).thenReturn(mockSuccessResult);

        // When & Then
        mockMvc.perform(get("/template/type")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        verify(templateService, times(1)).findTemplateType();
    }

    @Test
    @DisplayName("测试获取模板类型下拉框列表 - 业务逻辑失败")
    @WithMockUser
    void testFindTemplateType_BusinessFail() throws Exception {
        // Given
        when(templateService.findTemplateType()).thenReturn(mockFailResult);

        // When & Then
        mockMvc.perform(get("/template/type")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("操作失败"));

        verify(templateService, times(1)).findTemplateType();
    }

    @Test
    @DisplayName("测试获取模板类型下拉框列表 - 未认证用户")
    void testFindTemplateType_Unauthenticated() throws Exception {
        // When & Then
        mockMvc.perform(get("/template/type")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isUnauthorized());

        verify(templateService, never()).findTemplateType();
    }

    @Test
    @DisplayName("测试CSRF保护 - 缺少CSRF令牌")
    @WithMockUser(authorities = "template:template:addorupdate")
    void testCSRFProtection_MissingToken() throws Exception {
        // When & Then
        mockMvc.perform(post("/template")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateVo)))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(templateService, never()).addAndUpdate(any(TemplateVo.class));
    }

    @Test
    @DisplayName("测试HTTP方法不支持")
    @WithMockUser(authorities = "template:template:list")
    void testUnsupportedHttpMethod() throws Exception {
        // When & Then
        mockMvc.perform(put("/template")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isMethodNotAllowed());
    }

    @Test
    @DisplayName("测试内容类型不支持")
    @WithMockUser(authorities = "template:template:addorupdate")
    void testUnsupportedMediaType() throws Exception {
        // When & Then
        mockMvc.perform(post("/template")
                .with(csrf())
                .contentType(MediaType.TEXT_PLAIN)
                .content("plain text content"))
                .andDo(print())
                .andExpect(status().isUnsupportedMediaType());

        verify(templateService, never()).addAndUpdate(any(TemplateVo.class));
    }
}
