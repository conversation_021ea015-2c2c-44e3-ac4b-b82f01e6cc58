package com.gl.service.template.controller;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;

/**
 * 模板控制器测试专用的Spring Boot应用类
 * 用于解决多个@SpringBootConfiguration冲突的问题
 */
@SpringBootApplication(scanBasePackages = { "com.gl.service.template" }, exclude = {
        RedisAutoConfiguration.class,
        RedisRepositoriesAutoConfiguration.class
})
public class TemplateTestApplication {

    public static void main(String[] args) {
        SpringApplication.run(TemplateTestApplication.class, args);
    }
}
