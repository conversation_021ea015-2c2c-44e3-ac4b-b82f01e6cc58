package com.gl.service.template.controller;

import com.gl.framework.security.service.PermissionService;
import com.gl.framework.security.handle.AuthenticationEntryPointImpl;
import com.gl.framework.security.handle.LogoutSuccessHandlerImpl;
import com.gl.framework.security.filter.JwtAuthenticationTokenFilter;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.core.annotation.Order;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * 模板控制器测试配置类
 * 提供测试环境所需的基本配置
 */
@TestConfiguration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@Order(99)
public class TemplateTestConfiguration extends WebSecurityConfigurerAdapter {

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.csrf().disable()
                .authorizeRequests()
                .anyRequest().permitAll();
    }

    /**
     * 提供模拟的权限服务Bean
     */
    @Bean("ps")
    @Primary
    public PermissionService permissionService() {
        PermissionService mockService = mock(PermissionService.class);
        // 配置权限检查返回值
        when(mockService.hasPermi("template:template:list")).thenReturn(true);
        when(mockService.hasPermi("template:template:export")).thenReturn(true);
        when(mockService.hasPermi("template:template:addorupdate")).thenReturn(true);
        when(mockService.hasPermi("template:template:delete")).thenReturn(true);
        when(mockService.hasPermi("other:permission")).thenReturn(false);
        return mockService;
    }

    /**
     * 提供模拟的UserDetailsService Bean
     */
    @Bean("userDetailsServiceImpl")
    @Primary
    public UserDetailsService userDetailsService() {
        return mock(UserDetailsService.class);
    }

    /**
     * 提供模拟的AuthenticationEntryPointImpl Bean
     */
    @Bean
    @Primary
    public AuthenticationEntryPointImpl authenticationEntryPoint() {
        return mock(AuthenticationEntryPointImpl.class);
    }

    /**
     * 提供模拟的LogoutSuccessHandlerImpl Bean
     */
    @Bean
    @Primary
    public LogoutSuccessHandlerImpl logoutSuccessHandler() {
        return mock(LogoutSuccessHandlerImpl.class);
    }

    /**
     * 提供模拟的JwtAuthenticationTokenFilter Bean
     */
    @Bean
    @Primary
    public JwtAuthenticationTokenFilter jwtAuthenticationTokenFilter() {
        return mock(JwtAuthenticationTokenFilter.class);
    }

}
