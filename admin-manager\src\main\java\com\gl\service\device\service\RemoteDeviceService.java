package com.gl.service.device.service;

import com.gl.service.device.repository.DeviceRepository;
import com.gl.service.opus.entity.Device;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date: 2025/3/20
 * @description:
 */
@Slf4j
@Service
@Scope("prototype")
@Transactional(rollbackFor = Exception.class)
public class RemoteDeviceService {

    @Resource
    private DeviceRepository deviceRepository;
    @Resource
    private JdbcTemplate jdbcTemplate;
    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;
    @Autowired
    private PyMqttService pyMqttService;

    private void checkDevice(Long deviceId) {
        boolean dev = deviceRepository.existsById(deviceId);
        if (!dev) {
            throw new NotFoundException("设备不存在");
        }

    }

    /**
     * 更新音量
     *
     * @param params
     * @return
     */
    public Result updateVolume(UpdateVolumeParams params, Long userId) {
        checkDevice(params.getDeviceId());
        Device dev = deviceRepository.getOne(params.getDeviceId());
        Result resultBean = pyMqttService.setVolume(dev.getSn(), params.getVolume(), userId);
        if ("10000".equals(resultBean.getCode())) {
            dev.setVolume(params.getVolume());
            deviceRepository.save(dev);
        }
        return resultBean;
    }

    /**
     * 删除音频
     *
     * @param params
     * @return
     */
    public Result delAudio(DelAudioParams params, Long userId) {
        checkDevice(params.getDeviceId());
        Device dev = deviceRepository.getOne(params.getDeviceId());
        Result resultBean = pyMqttService.delAudio(dev.getSn(), params.getAudioName(), userId);
        if ("10000".equals(resultBean.getCode())) {
            String sql = "delete from dub_device_voice where device_id = ?  and title = ? ";
            jdbcTemplate.update(sql, params.getDeviceId(), params.getAudioName());
        }
        return resultBean;
    }

    /**
     * 获取时间
     *
     * @param deviceId
     * @return
     */
    public Result getTime(Long deviceId, Long userId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        Result resultBean = pyMqttService.setTime(dev.getSn(), userId);
        log.info("获取时间：{}", JSONObject.toJSON(resultBean));
        return resultBean;
    }

    /**
     * 获取音乐
     *
     * @param deviceId
     * @return
     */
    public Result getMusic(Long deviceId, Long userId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        Result resultBean = pyMqttService.getAudioList(dev.getSn(), userId);
        log.info("获取音乐：{}", JSONObject.toJSON(resultBean));
        return resultBean;
    }

    /**
     * 试听音乐
     *
     * @param deviceId
     * @param audioName
     * @return
     */
    public Result listenAudio(Long deviceId, String audioName, Long userId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        Result resultBean = pyMqttService.tryAudio(dev.getSn(), audioName, userId);
        log.info("试听音乐：{}", JSONObject.toJSON(resultBean));
        return resultBean;
    }

    /**
     * 获取磁盘
     *
     * @param deviceId
     * @return
     */
    public Result getSpace(Long deviceId, Long userId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        Result resultBean = pyMqttService.getDisk(dev.getSn(), userId);
        log.info("获取磁盘：{}", JSONObject.toJSON(resultBean));
        return resultBean;
    }

    /**
     * 获取音量
     *
     * @param deviceId
     * @return
     */
    public Result getVolume(Long deviceId, Long userId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        return pyMqttService.getVolume(dev.getSn(), userId);
    }

    /**
     * 定时播放
     *
     * @param deviceId
     * @param audioId
     * @return
     */
    public Result setTimePlay(Long deviceId, String audioId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        Map<String, Object> map = new HashMap<>();
        map.put("msg", "此功能还没完善，需要对接，请联系开发");
        return ResultBean.successfulResult(map);
    }

    /**
     * 复制音频
     *
     * @param deviceId
     * @return
     */
    public Result transmissionAudio(Long deviceId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        Map<String, Object> map = new HashMap<>();
        map.put("msg", "此功能还没完善，需要对接，请联系开发");
        return ResultBean.successfulResult(map);
    }

    /**
     * 发送音频
     *
     * @param deviceId
     * @return
     */
    public Result sendAudio(Long deviceId, String audioUrl, Long userId, String name) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        return pyMqttService.urlCmd(dev.getSn(), audioUrl, name, userId);
    }

    /**
     * 扫描蓝牙
     *
     * @param deviceId
     * @return
     */
    public Result scanBluetooth(Long deviceId, Long userId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        return pyMqttService.getBluetoothList(dev.getSn(), userId);
    }

    /**
     * 扫描蓝牙
     *
     * @param deviceId
     * @return
     */
    public Result getBluetooth(Long deviceId, Long userId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        String payload = redisTemplate.opsForValue().get(dev.getSn());
        Map<String, Object> map = new HashMap<>();
        map.put("list", payload);
        return ResultBean.successfulResult(map);
    }
}
