package com.gl.service.shop.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.web.response.Result;
import com.gl.service.shop.service.ShopService;
import com.gl.service.shop.vo.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * ShopController 单元测试类
 *
 * @author: test
 * @date: 2025-01-11
 * @version: 1.0
 */
@ExtendWith(MockitoExtension.class)
class ShopControllerTest {

    private MockMvc mockMvc;

    @Mock
    private ShopService shopService;

    @InjectMocks
    private ShopController shopController;

    private ObjectMapper objectMapper;

    // 测试数据
    private ShopQueryParamVo queryParamVo;
    private ShopAddVo shopAddVo;
    private ShopExamineVo shopExamineVo;
    private Result successResult;
    private Result failResult;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(shopController).build();
        objectMapper = new ObjectMapper();
        
        // 初始化查询参数VO
        queryParamVo = new ShopQueryParamVo();
        queryParamVo.setShopName("测试门店");
        queryParamVo.setStatus(1);
        queryParamVo.setType(1);
        queryParamVo.setShopIds(Arrays.asList(1L, 2L));

        // 初始化门店新增/修改VO
        shopAddVo = new ShopAddVo();
        shopAddVo.setId(1L);
        shopAddVo.setShopName("测试门店");
        shopAddVo.setUserId(1L);
        shopAddVo.setUserPhone("13800138000");
        shopAddVo.setUserName("测试用户");

        // 初始化门店审核VO
        shopExamineVo = new ShopExamineVo();
        shopExamineVo.setId(1L);
        shopExamineVo.setStatus(1);

        // 初始化结果对象
        successResult = Result.success("操作成功");
        failResult = Result.fail("操作失败");
    }

    /**
     * 测试门店列表查询 - 成功场景
     */
    @Test
    void testList_Success() throws Exception {
        // Given
        when(shopService.list(any(ShopQueryParamVo.class))).thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/shop")
                .param("shopName", "测试门店")
                .param("status", "1")
                .param("type", "1")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).list(any(ShopQueryParamVo.class));
    }

    /**
     * 测试新增门店 - 成功场景
     */
    @Test
    void testAdd_Success() throws Exception {
        // Given
        when(shopService.add(any(ShopAddVo.class))).thenReturn(successResult);

        // When & Then
        mockMvc.perform(post("/shop")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(shopAddVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).add(any(ShopAddVo.class));
    }

    /**
     * 测试修改门店 - 成功场景
     */
    @Test
    void testEdit_Success() throws Exception {
        // Given
        when(shopService.update(any(ShopAddVo.class))).thenReturn(successResult);

        // When & Then
        mockMvc.perform(put("/shop")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(shopAddVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).update(any(ShopAddVo.class));
    }

    /**
     * 测试删除门店 - 成功场景
     */
    @Test
    void testRemove_Success() throws Exception {
        // Given
        when(shopService.deleteByIds(anyList())).thenReturn(successResult);

        // When & Then
        mockMvc.perform(delete("/shop")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryParamVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).deleteByIds(anyList());
    }

    /**
     * 测试门店详情查询 - 成功场景
     */
    @Test
    void testDetail_Success() throws Exception {
        // Given
        when(shopService.detailById(1L)).thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/shop/detail")
                .param("id", "1")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).detailById(1L);
    }

    /**
     * 测试门店审核 - 成功场景
     */
    @Test
    void testExamine_Success() throws Exception {
        // Given
        when(shopService.updateByStatus(any(ShopExamineVo.class))).thenReturn(successResult);

        // When & Then
        mockMvc.perform(post("/shop/examine")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(shopExamineVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        verify(shopService, times(1)).updateByStatus(any(ShopExamineVo.class));
    }

    /**
     * 测试服务层返回失败结果的场景
     */
    @Test
    void testList_ServiceReturnsFail() throws Exception {
        // Given
        when(shopService.list(any(ShopQueryParamVo.class))).thenReturn(failResult);

        // When & Then
        mockMvc.perform(get("/shop")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10001))
                .andExpect(jsonPath("$.message").value("操作失败"));

        verify(shopService, times(1)).list(any(ShopQueryParamVo.class));
    }
}
