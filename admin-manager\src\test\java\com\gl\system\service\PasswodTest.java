package com.gl.system.service;

import com.gl.framework.common.util.SecurityUtils;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

public class PasswodTest {

    private static final Logger log = LoggerFactory.getLogger(PasswodTest.class);

    @Test
    public void password() {
        String password = SecurityUtils.encryptPassword("a12345678");
        log.info("password:{}", password);
    }

    @Test
    public void encode() throws UnsupportedEncodingException {
        String str = URLEncoder.encode("&", StandardCharsets.UTF_8.toString());
        log.info("str:{}", str);
    }
}
