package com.gl.service.device.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date: 2025/3/20
 * @description:
 */
@Slf4j
@Service
public class PyMqttService {

    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;
    @Autowired
    private MqttClientConfig mqttClientConfig;

    @Value("${ali.oss.bucket.url}")
    private String aliUrl;

    /**
     * 设置音量 1加 0减
     * 1.设置音量加: {“cmd”:”sysVolume”,”parm”:”0”} //音量加
     * 2.设置音量减: {“cmd”:”sysVolume”,”parm”:”1”} //音量减
     */
    public ResultBean<Object> setVolume(String sn, Integer parm, Long userId) {
        String timeStamp = getTimestamp();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "sysVolume");
        jsonObject.put("parm", parm);
        jsonObject.put("timeStamp", timeStamp);
        String msg = mqttClientConfig.publish(sn, jsonObject.toJSONString(), userId, timeStamp, 15);
        log.info("返回消息 msg = {}", msg);
        return getResultBean(sn, msg, timeStamp);
    }

    private ResultBean<Object> getResultBean(String sn, String msg, String timeStamp) {
        Map<String, Object> map = new HashMap<>();

        if (msg == null) {
            String response = getTopicResponse(sn, timeStamp);
            if (StringUtils.isNotBlank(response)) {
                map.put("response", response);
                try {
                    JSONObject respObj = JSONObject.parseObject(response);
                    String param = respObj.getString("param");
                    if (param != null && param.equals("err")) {
                        map.put("cause", "硬件执行失败" + param);
                        return ResultBean.failedResult(map);
                    }
                } catch (Exception ignored) {
                }
                return ResultBean.successfulResult(map);
            } else {
                map.put("cause", "超时没有返回响应");
            }
        }

        map.put("cause", msg);
        return ResultBean.failedResult(map);
    }

    /**
     * 设置音乐
     * 3.设置上一曲：{“cmd”:”playmusic”,”parm”:”0”} //上一曲
     * 4.设置下一曲：{“cmd”:”playmusic”,”parm”:”1”} //下一曲
     * 5.停止播放： {“cmd”:”playmusic”,”parm”:”2”} //停止播放
     * 6.开关机： {“cmd”:”playmusic”,”parm”:”3”} //开关机
     */
    public ResultBean<Object> setMusic(String sn, Integer status, Long userId) {

        String timeStamp = getTimestamp();
        String cmd = "playmusic";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", cmd);
        jsonObject.put("parm", status);
        jsonObject.put("timeStamp", timeStamp);
        // 阻塞等待返回消息，超时时间为 10 秒
        String msg = mqttClientConfig.publish(sn, jsonObject.toJSONString(), userId, timeStamp, 15);
        return getResultBean(sn, msg, timeStamp);
    }

    /**
     * 7.删除音频:{“cmd”:”deleteRing”,”parm”:”151”}
     */
    public ResultBean<Object> delAudio(String sn, String audioName, Long userId) {
        String timeStamp = getTimestamp();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "deleteRing");
        jsonObject.put("parm", audioName);
        jsonObject.put("timeStamp", timeStamp);
        String msg = mqttClientConfig.publish(sn, jsonObject.toJSONString(), userId, timeStamp, 15);
        return getResultBean(sn, msg, timeStamp);
    }

    /**
     * 8.新增铃声方案：
     * {
     * “cmd”:”addCase”,
     * ”parm”: ""
     * ”period":
     * [
     * {
     * "caseDay":"mon,tue,thur",//周几配置 使用英文简写标识或者简单字符0-6也可以
     * "startTime":"09:05",
     * "endTime":"10:10",
     * "ringid","151",
     * “caseid”:”0”
     * }
     * ]
     * }
     * //最对设置3组 caseid对应 0 1 2
     */
    public ResultBean<Object> addCase(String sn, CaseParams map, Long userId) {
        String timeStamp = getTimestamp();
        map.setTimeStamp(timeStamp);

        String msg = mqttClientConfig.publish(sn, JSONObject.toJSONString(map), userId, timeStamp, 15);
        return getResultBean(sn, msg, timeStamp);
    }

    /**
     * 9.删除铃声方案{“cmd“:”deleteCase”,”parm”:”0”}
     * 10.编辑铃声方案{“cmd“:”editCase”,”parm”:”同新增铃声方案，直接覆盖”}
     * 11、方案开启 {“cmd”:”activeCase”,”parm”:" caseid ":"0"}
     * 12、方案关闭 {“cmd”:”closeCase”,”parm”:" caseid ":"1"}
     */
    public ResultBean<Object> editCase(String sn, CaseParams map, Long userId) {
        String timeStamp = getTimestamp();
        map.setTimeStamp(timeStamp);
        String msg = mqttClientConfig.publish(sn, JSONObject.toJSONString(map), userId, timeStamp, 15);
        return getResultBean(sn, msg, timeStamp);
    }

    public ResultBean<Object> delCase(String sn, Integer caseId, Long userId) {
        String timeStamp = getTimestamp();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "deleteCase");
        jsonObject.put("parm", caseId);
        jsonObject.put("timeStamp", timeStamp);
        String msg = mqttClientConfig.publish(sn, jsonObject.toJSONString(), userId, timeStamp, 15);
        return getResultBean(sn, msg, timeStamp);
    }

    public ResultBean<Object> activeCase(String sn, Integer caseId, Long userId) {
        String timeStamp = getTimestamp();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "activeCase");
        jsonObject.put("parm", caseId);
        jsonObject.put("timeStamp", timeStamp);
        String msg = mqttClientConfig.publish(sn, jsonObject.toJSONString(), userId, timeStamp, 15);
        return getResultBean(sn, msg, timeStamp);
    }

    public ResultBean<Object> closeCase(String sn, Integer caseId, Long userId) {
        String timeStamp = getTimestamp();

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "closeCase");
        jsonObject.put("parm", caseId);
        jsonObject.put("timeStamp", timeStamp);
        String msg = mqttClientConfig.publish(sn, jsonObject.toJSONString(), userId, timeStamp, 15);
        return getResultBean(sn, msg, timeStamp);
    }

    /**
     * 13、设置时钟 {“cmd”:”setTime”,”parm”:””} //获取手机的时间
     * 14、获取本地音频文件列表{“cmd”:”getAudioList”,”parm”:””}
     * 返回内容 {“status”:”0”,”list”:[{”name”:”音频1”,”id”:”151”},
     * {“name”:”音频2”,”id”:”152”}]
     * {“name”:”音频3”,”id”:”153”}}
     * 15、试听本地音频文件{“cmd”:”try”,”parm”:”151”}
     * 16、获取剩余空间{“cmd”:” getDisk”,”parm”:””}
     * 17、获取铃声方案{“cmd”:” getCase”,”parm”:””}
     * 18、发送音频地址{“cmd”:” url_cmd”,”parm”:””}
     */
    public ResultBean<Object> setTime(String sn, Long userId) {
        String timeStamp = getTimestamp();
        String cmd = "setTime";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", cmd);
        jsonObject.put("parm", "");
        jsonObject.put("timeStamp", timeStamp);
        String msg = mqttClientConfig.publish(sn, jsonObject.toJSONString(), userId, timeStamp, 15);
        return getResultBean(sn, msg, timeStamp);
    }

    public ResultBean<Object> getAudioList(String sn, Long userId) {
        String timeStamp = getTimestamp();
        String cmd = "getAudioList";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", cmd);
        jsonObject.put("parm", "");
        jsonObject.put("timeStamp", timeStamp);
        String msg = mqttClientConfig.publish(sn, jsonObject.toJSONString(), userId, timeStamp, 15);
        return getResultBean(sn, msg, timeStamp);
    }

    public ResultBean<Object> tryAudio(String sn, String audioId, Long userId) {
        String timeStamp = getTimestamp();
        String cmd = "try";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", cmd);
        jsonObject.put("parm", audioId);
        jsonObject.put("timeStamp", timeStamp);
        String msg = mqttClientConfig.publish(sn, jsonObject.toJSONString(), userId, timeStamp, 15);
        return getResultBean(sn, msg, timeStamp);
    }

    public ResultBean<Object> getDisk(String sn, Long userId) {
        String timeStamp = getTimestamp();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "getDisk");
        jsonObject.put("parm", "");
        jsonObject.put("timeStamp", timeStamp);
        String msg = mqttClientConfig.publish(sn, jsonObject.toJSONString(), userId, timeStamp, 15);
        // 阻塞等待返回消息，超时时间为 10 秒
        return getResultBean(sn, msg, timeStamp);
    }

    public ResultBean<Object> getCase(String sn, Long userId) {
        String timeStamp = getTimestamp();

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "getCase");
        jsonObject.put("parm", "");
        jsonObject.put("timeStamp", timeStamp);
        String msg = mqttClientConfig.publish(sn, jsonObject.toJSONString(), userId, timeStamp, 15);
        return getResultBean(sn, msg, timeStamp);
    }

    public ResultBean<Object> urlCmd(String sn, String url, String name, Long userId) {
        String timeStamp = getTimestamp();

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "url_cmd");
        jsonObject.put("parm", url);
        jsonObject.put("name", name);
        jsonObject.put("timeStamp", timeStamp);
        String msg = mqttClientConfig.publish(sn, jsonObject.toJSONString(), userId, timeStamp, 15);
        return getResultBean(sn, msg, timeStamp);
    }

    public ResultBean<Object> getVolume(String sn, Long userId) {
        String timeStamp = getTimestamp();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "getSysVolume");
        jsonObject.put("parm", "");
        jsonObject.put("timeStamp", timeStamp);
        String msg = mqttClientConfig.publish(sn, jsonObject.toJSONString(), userId, timeStamp, 15);
        return getResultBean(sn, msg, timeStamp);
    }

    public ResultBean<Object> getBluetoothList(String sn, Long userId) {
        String timeStamp = getTimestamp();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "edr_name");
        jsonObject.put("parm", "0");
        jsonObject.put("timeStamp", timeStamp);
        String msg = mqttClientConfig.publish(sn, jsonObject.toJSONString(), userId, timeStamp, 15);
        return getResultBean(sn, msg, timeStamp);
    }

    // 生成时间戳
    public synchronized String getTimestamp() {
        long tt = System.currentTimeMillis();
        String timestamp = "2-" + String.valueOf(tt);
        while (true) {
            tt = System.currentTimeMillis();
            timestamp = "2-" + String.valueOf(tt);
            String redisTT = redisTemplate.opsForValue().get(timestamp);
            if (org.apache.commons.lang3.StringUtils.isEmpty(redisTT)) {
                break;
            }
        }
        return timestamp;
    }

    // 获取MQTT返回的结果
    private String getTopicResponse(String sn, String timeStamp) {

        String respKey = String.format(RedisKeyConstant.DEVICE_RESP, sn, timeStamp);

        log.info("获取结果 sn={}", sn);
        String payload = null;
        int index = 1;
        while (true) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
            if (index > 15) {
                log.info("没有获取到结果，超时返回, sn={}", sn);
                break;
            }

            payload = redisTemplate.opsForValue().get(respKey);
            if (StringUtils.isNotEmpty(payload)) {
                break;
            }

            index++;
        }
        redisTemplate.delete(respKey);
        return payload;
    }
}
