package com.gl.service.shop.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.redis.RedisUtils;
import com.gl.framework.exception.CustomException;
import com.gl.framework.security.LoginUser;
import com.gl.framework.security.service.TokenService;
import com.gl.framework.security.service.UserDetailsServiceImpl;
import com.gl.framework.web.response.Result;
import com.gl.service.music.vo.BackGroundMusicVo;
import com.gl.service.opus.dto.AnchorDTO;
import com.gl.service.opus.entity.FollowAnchor;
import com.gl.service.opus.entity.LongFollowAnchor;
import com.gl.service.opus.entity.UserFollowBgm;
import com.gl.service.opus.entity.UserTextTemplate;
import com.gl.service.opus.repository.BackgroundMusicRepository;
import com.gl.service.opus.repository.DubAnchorRepository;
import com.gl.service.opus.repository.LongAnchorRepository;
import com.gl.service.shop.controller.request.FollowAnchorReq;
import com.gl.service.shop.controller.request.FollowBgmReq;
import com.gl.service.shop.controller.request.TextTemplateAddReq;
import com.gl.service.shop.controller.request.TextTemplateDelReq;
import com.gl.service.shop.repository.FollowAnchorRepository;
import com.gl.service.shop.repository.LongFollowAnchorRepository;
import com.gl.service.shop.repository.UserFollowBgmRepository;
import com.gl.service.shop.repository.UserTextTemplateRepository;
import com.gl.service.template.vo.TemplateVo;
import com.gl.system.repository.SysUserDeptRepository;
import com.gl.system.repository.SysUserRepository;
import com.gl.system.repository.SysUserRoleRepository;
import com.gl.system.vo.SysUserVo;
import com.gl.wechat.WeCharUserInfo;
import com.gl.wechat.repository.WechatUserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * WeChatService单元测试类
 * 测试微信服务的所有公共方法，包括登录认证、用户关注管理、文本模板管理等功能
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("微信服务单元测试")
class WeChatServiceTest {

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private RedisUtils redisUtils;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private TokenService tokenService;

    @Mock
    private UserDetailsServiceImpl userDetailsService;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private WechatUserRepository wechatUserRepository;

    @Mock
    private SysUserRepository userRepository;

    @Mock
    private SysUserDeptRepository sysUserDeptRepository;

    @Mock
    private SysUserRoleRepository sysUserRoleRepository;

    @Mock
    private FollowAnchorRepository followAnchorRepository;

    @Mock
    private LongFollowAnchorRepository longFollowAnchorRepository;

    @Mock
    private UserFollowBgmRepository userFollowBgmRepository;

    @Mock
    private UserTextTemplateRepository userTextTemplateRepository;

    @Mock
    private DubAnchorRepository dubAnchorRepository;

    @Mock
    private LongAnchorRepository longAnchorRepository;

    @Mock
    private BackgroundMusicRepository backgroundMusicRepository;

    @InjectMocks
    private WeChatService weChatService;

    private static final String TEST_APP_ID = "test_app_id";
    private static final String TEST_APP_SECRET = "test_app_secret";
    private static final String TEST_REDIRECT_URI = "http://test.com/callback";
    private static final Long TEST_USER_ID = 123L;
    private static final String TEST_CODE = "test_code";
    private static final String TEST_STATE = "test_state";
    private static final String TEST_UUID = "test_uuid";

    @BeforeEach
    void setUp() {
        // 使用反射设置私有字段
        try {
            java.lang.reflect.Field appIdField = WeChatService.class.getDeclaredField("appId");
            appIdField.setAccessible(true);
            appIdField.set(weChatService, TEST_APP_ID);

            java.lang.reflect.Field appSecretField = WeChatService.class.getDeclaredField("appSecret");
            appSecretField.setAccessible(true);
            appSecretField.set(weChatService, TEST_APP_SECRET);

            java.lang.reflect.Field redirectUriField = WeChatService.class.getDeclaredField("redirectUri");
            redirectUriField.setAccessible(true);
            redirectUriField.set(weChatService, TEST_REDIRECT_URI);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set up test fields", e);
        }
    }

    @Test
    @DisplayName("测试生成微信登录URL - 成功场景")
    void testWebLogin_Success() {
        // Given
        HttpServletRequest request = mock(HttpServletRequest.class);

        // When
        Map<String, Object> result = weChatService.webLogin(request);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertTrue(result.containsKey("loginUrl"), "结果应包含loginUrl");
        assertTrue(result.containsKey("state"), "结果应包含state");

        String loginUrl = (String) result.get("loginUrl");
        assertNotNull(loginUrl, "登录URL不应为空");
        assertTrue(loginUrl.contains(TEST_APP_ID), "登录URL应包含appId");
        assertTrue(loginUrl.contains("snsapi_login"), "登录URL应包含正确的scope");

        String state = (String) result.get("state");
        assertNotNull(state, "state不应为空");
        assertFalse(state.isEmpty(), "state不应为空字符串");
    }

    @Test
    @DisplayName("测试检查登录状态 - 用户已登录")
    void testCheckLoginStatus_UserLoggedIn() {
        // Given
        WeCharUserInfo userInfo = new WeCharUserInfo();
        userInfo.setLoggedIn(true);
        userInfo.setToken("test_token");
        userInfo.setNickname("test_user");

        when(redisUtils.get(TEST_UUID)).thenReturn(userInfo);

        // When
        Map<String, Object> result = weChatService.checkLoginStatus(TEST_UUID);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertTrue((Boolean) result.get("loggedIn"), "用户应该已登录");
        assertEquals("test_token", result.get("token"), "应返回正确的token");
        assertEquals(userInfo, result.get("userInfo"), "应返回用户信息");

        verify(redisUtils).del(TEST_UUID);
    }

    @Test
    @DisplayName("测试检查登录状态 - 用户未登录")
    void testCheckLoginStatus_UserNotLoggedIn() {
        // Given
        when(redisUtils.get(TEST_UUID)).thenReturn(null);

        // When
        Map<String, Object> result = weChatService.checkLoginStatus(TEST_UUID);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertFalse((Boolean) result.get("loggedIn"), "用户应该未登录");
        assertFalse(result.containsKey("token"), "不应包含token");
        assertFalse(result.containsKey("userInfo"), "不应包含用户信息");

        verify(redisUtils, never()).del(TEST_UUID);
    }

    @Test
    @DisplayName("测试生成微信更新URL - 成功场景")
    void testUpdateWx_Success() {
        // Given
        HttpServletRequest request = mock(HttpServletRequest.class);

        // When
        Map<String, Object> result = weChatService.updateWx(request);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertTrue(result.containsKey("loginUrl"), "结果应包含loginUrl");
        assertTrue(result.containsKey("state"), "结果应包含state");

        String loginUrl = (String) result.get("loginUrl");
        assertNotNull(loginUrl, "登录URL不应为空");
        assertTrue(loginUrl.contains("/user/profile"), "更新URL应包含正确的回调路径");
    }

    @Test
    @DisplayName("测试关注主播 - 首次关注")
    void testFollowAnchor_FirstTimeFollow() {
        // Given
        FollowAnchorReq request = new FollowAnchorReq();
        request.setAnchorId(1L);

        SysUserVo userVo = new SysUserVo();
        userVo.setSiteId(TEST_USER_ID);
        LoginUser loginUser = new LoginUser();
        loginUser.setUser(userVo);

        when(followAnchorRepository.findByAnchorIdAndUserId(1L, TEST_USER_ID))
                .thenReturn(Collections.emptyList());

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(loginUser);

            // When
            Result result = weChatService.followAnchor(request);

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(10000, result.getCode(), "应返回成功状态码");

            verify(followAnchorRepository).save(any(FollowAnchor.class));
            verify(followAnchorRepository, never()).delete(any(FollowAnchor.class));
        }
    }

    @Test
    @DisplayName("测试关注主播 - 取消关注")
    void testFollowAnchor_UnFollow() {
        // Given
        FollowAnchorReq request = new FollowAnchorReq();
        request.setAnchorId(1L);

        SysUserVo userVo = new SysUserVo();
        userVo.setSiteId(TEST_USER_ID);
        LoginUser loginUser = new LoginUser();
        loginUser.setUser(userVo);

        FollowAnchor existingFollow = new FollowAnchor();
        existingFollow.setAnchorId(1L);
        existingFollow.setUserId(TEST_USER_ID);

        when(followAnchorRepository.findByAnchorIdAndUserId(1L, TEST_USER_ID))
                .thenReturn(Arrays.asList(existingFollow));

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(loginUser);

            // When
            Result result = weChatService.followAnchor(request);

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(10000, result.getCode(), "应返回成功状态码");

            verify(followAnchorRepository, never()).save(any(FollowAnchor.class));
            verify(followAnchorRepository).delete(existingFollow);
        }
    }

    @Test
    @DisplayName("测试关注长音频主播 - 成功场景")
    void testFollowLongAnchor_Success() {
        // Given
        FollowAnchorReq request = new FollowAnchorReq();
        request.setAnchorId(2L);

        SysUserVo userVo = new SysUserVo();
        userVo.setSiteId(TEST_USER_ID);
        LoginUser loginUser = new LoginUser();
        loginUser.setUser(userVo);

        when(longFollowAnchorRepository.findByAnchorIdAndUserId(2L, TEST_USER_ID))
                .thenReturn(Collections.emptyList());

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(loginUser);

            // When
            Result result = weChatService.followLongAnchor(request);

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(10000, result.getCode(), "应返回成功状态码");

            verify(longFollowAnchorRepository).save(any(LongFollowAnchor.class));
        }
    }

    @Test
    @DisplayName("测试关注背景音乐 - 成功场景")
    void testFollowBgm_Success() {
        // Given
        FollowBgmReq request = new FollowBgmReq();
        request.setBgmId(3L);

        SysUserVo userVo = new SysUserVo();
        userVo.setSiteId(TEST_USER_ID);
        LoginUser loginUser = new LoginUser();
        loginUser.setUser(userVo);

        when(userFollowBgmRepository.findByBgmIdAndUserId(3L, TEST_USER_ID))
                .thenReturn(Collections.emptyList());

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(loginUser);

            // When
            Result result = weChatService.followBgm(request);

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(10000, result.getCode(), "应返回成功状态码");

            verify(userFollowBgmRepository).save(any(UserFollowBgm.class));
        }
    }

    @Test
    @DisplayName("测试获取我的VIP主播 - 成功场景")
    void testGetMyVipAnchor_Success() {
        // Given
        SysUserVo userVo = new SysUserVo();
        userVo.setSiteId(TEST_USER_ID);
        LoginUser loginUser = new LoginUser();
        loginUser.setUser(userVo);

        List<AnchorDTO> mockAnchors = Arrays.asList(
                new AnchorDTO(1L, "主播1", "场景1", "类型1", "url1", "voice1"),
                new AnchorDTO(2L, "主播2", "场景2", "类型2", "url2", "voice2"));

        when(dubAnchorRepository.sailuo(TEST_USER_ID)).thenReturn(mockAnchors);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(loginUser);

            // When
            Result result = weChatService.getMyVipAnchor();

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(10000, result.getCode(), "应返回成功状态码");

            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) result.getData();
            assertNotNull(data, "数据不应为空");
            assertTrue(data.containsKey("result"), "应包含result字段");

            @SuppressWarnings("unchecked")
            List<AnchorDTO> anchors = (List<AnchorDTO>) data.get("result");
            assertEquals(2, anchors.size(), "应返回2个主播");
            assertEquals("主播1", anchors.get(0).getName(), "第一个主播名称应正确");
        }
    }

    @Test
    @DisplayName("测试获取我的长音频主播 - 成功场景")
    void testGetMyLongAnchor_Success() {
        // Given
        SysUserVo userVo = new SysUserVo();
        userVo.setSiteId(TEST_USER_ID);
        LoginUser loginUser = new LoginUser();
        loginUser.setUser(userVo);

        List<AnchorDTO> mockAnchors = Arrays.asList(
                new AnchorDTO(3L, "长音频主播1", "长音频场景", "长音频类型", "url3", "voice3"));

        when(longAnchorRepository.taijia(TEST_USER_ID)).thenReturn(mockAnchors);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(loginUser);

            // When
            Result result = weChatService.getMyLongAnchor();

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(10000, result.getCode(), "应返回成功状态码");

            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) result.getData();
            assertNotNull(data, "数据不应为空");
            assertTrue(data.containsKey("result"), "应包含result字段");

            @SuppressWarnings("unchecked")
            List<AnchorDTO> anchors = (List<AnchorDTO>) data.get("result");
            assertEquals(1, anchors.size(), "应返回1个长音频主播");
            assertEquals("长音频主播1", anchors.get(0).getName(), "主播名称应正确");
        }
    }

    @Test
    @DisplayName("测试获取关注的背景音乐 - 成功场景")
    void testGetFollowBgm_Success() {
        // Given
        SysUserVo userVo = new SysUserVo();
        userVo.setSiteId(TEST_USER_ID);
        LoginUser loginUser = new LoginUser();
        loginUser.setUser(userVo);

        List<BackGroundMusicVo> mockBgms = Arrays.asList(
                new BackGroundMusicVo(1L, "背景音乐1", "url1", 120),
                new BackGroundMusicVo(2L, "背景音乐2", "url2", 180));

        when(backgroundMusicRepository.findFollowBgm(TEST_USER_ID)).thenReturn(mockBgms);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(loginUser);

            // When
            Result result = weChatService.getFollowBgm();

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(10000, result.getCode(), "应返回成功状态码");

            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) result.getData();
            assertNotNull(data, "数据不应为空");
            assertTrue(data.containsKey("result"), "应包含result字段");

            @SuppressWarnings("unchecked")
            List<BackGroundMusicVo> bgms = (List<BackGroundMusicVo>) data.get("result");
            assertEquals(2, bgms.size(), "应返回2个背景音乐");
            assertEquals("背景音乐1", bgms.get(0).getName(), "第一个背景音乐名称应正确");
        }
    }

    @Test
    @DisplayName("测试添加文本模板 - 新增模板")
    void testAddTextTemplate_NewTemplate() {
        // Given
        TextTemplateAddReq request = new TextTemplateAddReq();
        request.setTextContent("测试模板内容");

        SysUserVo userVo = new SysUserVo();
        userVo.setSiteId(TEST_USER_ID);
        LoginUser loginUser = new LoginUser();
        loginUser.setUser(userVo);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(loginUser);

            // When
            Result result = weChatService.addTextTemplate(request);

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(10000, result.getCode(), "应返回成功状态码");
            assertTrue((Boolean) result.getData(), "应返回true表示成功");

            verify(userTextTemplateRepository).save(argThat(template -> template.getTextContent().equals("测试模板内容") &&
                    template.getUserId().equals(TEST_USER_ID) &&
                    template.getId() == null));
        }
    }

    @Test
    @DisplayName("测试添加文本模板 - 更新现有模板")
    void testAddTextTemplate_UpdateExisting() {
        // Given
        TextTemplateAddReq request = new TextTemplateAddReq();
        request.setId(1L);
        request.setTextContent("更新的模板内容");

        SysUserVo userVo = new SysUserVo();
        userVo.setSiteId(TEST_USER_ID);
        LoginUser loginUser = new LoginUser();
        loginUser.setUser(userVo);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(loginUser);

            // When
            Result result = weChatService.addTextTemplate(request);

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(10000, result.getCode(), "应返回成功状态码");
            assertTrue((Boolean) result.getData(), "应返回true表示成功");

            verify(userTextTemplateRepository).save(argThat(template -> template.getTextContent().equals("更新的模板内容") &&
                    template.getUserId().equals(TEST_USER_ID) &&
                    template.getId().equals(1L)));
        }
    }

    @Test
    @DisplayName("测试获取文本模板 - 成功场景")
    void testGetTextTemplate_Success() {
        // Given
        SysUserVo userVo = new SysUserVo();
        userVo.setSiteId(TEST_USER_ID);
        LoginUser loginUser = new LoginUser();
        loginUser.setUser(userVo);

        List<UserTextTemplate> mockTemplates = Arrays.asList(
                createUserTextTemplate(1L, "模板内容1"),
                createUserTextTemplate(2L, "模板内容2"));

        when(userTextTemplateRepository.findByUserId(TEST_USER_ID)).thenReturn(mockTemplates);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(loginUser);

            // When
            Result result = weChatService.getTextTemplate();

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(10000, result.getCode(), "应返回成功状态码");

            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) result.getData();
            assertNotNull(data, "数据不应为空");
            assertTrue(data.containsKey("result"), "应包含result字段");

            @SuppressWarnings("unchecked")
            List<TemplateVo> templates = (List<TemplateVo>) data.get("result");
            assertEquals(2, templates.size(), "应返回2个模板");
            assertEquals("模板内容1", templates.get(0).getContent(), "第一个模板内容应正确");
            assertEquals(1L, templates.get(0).getId(), "第一个模板ID应正确");
        }
    }

    @Test
    @DisplayName("测试删除文本模板 - 成功场景")
    void testDelTextTemplate_Success() {
        // Given
        TextTemplateDelReq request = new TextTemplateDelReq();
        request.setId(1L);

        SysUserVo userVo = new SysUserVo();
        userVo.setSiteId(TEST_USER_ID);
        LoginUser loginUser = new LoginUser();
        loginUser.setUser(userVo);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(loginUser);

            // When
            Result result = weChatService.delTextTemplate(request);

            // Then
            assertNotNull(result, "返回结果不应为空");
            assertEquals(10000, result.getCode(), "应返回成功状态码");
            assertTrue((Boolean) result.getData(), "应返回true表示成功");

            verify(userTextTemplateRepository).deleteByIdAndUserId(1L, TEST_USER_ID);
        }
    }

    @Test
    @DisplayName("测试微信回调处理 - 成功场景")
    void testHandleCallback_Success() throws Exception {
        // 注意：由于handleCallback方法涉及实际的微信API调用和复杂的私有方法，
        // 这个测试主要验证异常情况。成功场景需要集成测试来验证。

        // Given
        String code = "invalid_code";
        String state = TEST_STATE;

        // When & Then - 验证无效code会抛出异常
        CustomException exception = assertThrows(CustomException.class,
                () -> weChatService.handleCallback(code, state),
                "应抛出CustomException");

        assertEquals("登录失败", exception.getMessage(), "异常消息应正确");
    }

    @Test
    @DisplayName("测试微信回调处理 - 获取token失败")
    void testHandleCallback_TokenFailure() {
        // Given
        String code = TEST_CODE;
        String state = TEST_STATE;

        // When & Then
        CustomException exception = assertThrows(CustomException.class,
                () -> weChatService.handleCallback(code, state),
                "应抛出CustomException");

        assertEquals("登录失败", exception.getMessage(), "异常消息应正确");
    }

    @Test
    @DisplayName("测试微信回调处理 - 解析token失败")
    void testHandleCallback_ParseTokenFailure() throws Exception {
        // Given
        String code = TEST_CODE;
        String state = TEST_STATE;

        // When & Then
        CustomException exception = assertThrows(CustomException.class,
                () -> weChatService.handleCallback(code, state),
                "应抛出CustomException");

        assertEquals("登录失败", exception.getMessage(), "异常消息应正确");
    }

    // 辅助方法
    private UserTextTemplate createUserTextTemplate(Long id, String content) {
        UserTextTemplate template = new UserTextTemplate();
        template.setId(id);
        template.setTextContent(content);
        template.setUserId(TEST_USER_ID);
        return template;
    }
}
