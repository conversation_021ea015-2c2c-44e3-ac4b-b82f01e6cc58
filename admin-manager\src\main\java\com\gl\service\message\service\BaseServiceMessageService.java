package com.gl.service.message.service;

import com.gl.framework.web.response.Result;
import com.gl.service.message.repository.BaseServiceMessageRepository;
import com.gl.service.message.vo.BaseServiceMessageVo;
import com.gl.service.message.vo.dto.BaseServiceMessageDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 客服信息
 *
 * @author: duanjinze
 * @date: 2022/11/11 17:05
 * @version: 1.0
 */
@Service
public class BaseServiceMessageService {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private BaseServiceMessageRepository baseServiceMessageRepository;

    public Result list(BaseServiceMessageDto dto) {
        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();
        if (dto != null) {
            if (StringUtils.isNotBlank(dto.getSearchCondition())) {
                where.append(" where d.content like ? ");
                args.add("%" + dto.getSearchCondition() + "%");
            }
        }
        String sql = "select d.id,dwu.avatar,dwu.nickname,dwu.phone,d.content,d.message_time from dub_base_service_message "
                + "d left join dub_wechat_user dwu on d.user_id = dwu.id";

        Result result = Result.success();
        Long count = jdbcTemplate.queryForObject("select count(1) from (" + sql + where + ") t", Long.class,
                args.toArray());
        if (count == null || count == 0) {
            result.addData("total", 0);
            result.addData("result", null);
            return result;
        }
        where.append(" order by d.message_time DESC ");
        where.append(" LIMIT ? OFFSET ? ");

        // 如果dto为null，使用默认分页参数
        int pageSize = (dto != null && dto.getPageSize() != null) ? dto.getPageSize() : 10;
        int pageNumber = (dto != null && dto.getPageNumber() != null) ? dto.getPageNumber() : 0;

        args.add(pageSize);
        args.add(pageNumber * pageSize);

        List<BaseServiceMessageVo> baseServiceMessageVos = jdbcTemplate.query(sql + where,
                new BeanPropertyRowMapper<>(BaseServiceMessageVo.class), args.toArray());
        result.addData("total", count);
        result.addData("result", baseServiceMessageVos);
        return result;
    }

    public Result delete(BaseServiceMessageDto dto) {
        if (dto == null) {
            return Result.fail("数据不能为空");
        }
        if (dto.getIds() == null || dto.getIds().isEmpty()) {
            return Result.fail("留言id不能为空");
        }
        baseServiceMessageRepository.deleteAllById(dto.getIds());
        return Result.success();
    }

}
