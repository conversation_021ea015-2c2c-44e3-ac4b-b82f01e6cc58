package com.gl.service.music.service;

import cn.hutool.core.collection.CollUtil;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.response.Result;
import com.gl.framework.web.response.ResultCode;
import com.gl.service.music.repository.BackgroundMusicTypeRepository;
import com.gl.service.music.vo.BackGroundMusicVo;
import com.gl.service.music.vo.dto.BackgroundMusicDto;
import com.gl.service.opus.entity.BackgroundMusic;
import com.gl.service.opus.entity.BackgroundMusicType;
import com.gl.service.opus.repository.BackgroundMusicRepository;
import com.gl.service.oss.service.OSSService;
import com.gl.system.vo.SysUserVo;
import com.gl.util.GetShopRefUtil;
import com.gl.util.VideoUtil;
import com.gl.util.WavFileParsingUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * BackgroundMusicService单元测试类
 *
 * 测试覆盖范围：
 * 1. list方法 - 背景音乐列表查询功能
 * 2. delete方法 - 背景音乐删除功能
 * 3. add方法 - 背景音乐添加功能
 * 4. findBackgroundMusicType方法 - 背景音乐类型查询功能
 *
 * <AUTHOR> Generator
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("BackgroundMusicService单元测试")
@SuppressWarnings("unchecked")
class BackgroundMusicServiceTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private OSSService ossService;

    @Mock
    private BackgroundMusicRepository backgroundMusicRepository;

    @Mock
    private BackgroundMusicTypeRepository backgroundMusicTypeRepository;

    @Mock
    private GetShopRefUtil shopRefUtil;

    @InjectMocks
    private BackgroundMusicService backgroundMusicService;

    private BackgroundMusicDto testDto;
    private BackGroundMusicVo testVo;
    private BackgroundMusic testEntity;
    private BackgroundMusicType testType;
    private SysUserVo testUser;
    private LoginUser testLoginUser;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        setupTestData();
    }

    /**
     * 初始化测试数据
     */
    private void setupTestData() {
        // 初始化DTO
        testDto = new BackgroundMusicDto();
        testDto.setPageNumber(0);
        testDto.setPageSize(10);
        testDto.setSearchCondition("测试音乐");
        testDto.setShopId(1L);
        testDto.setBackgroundMusicId(1L);

        // 初始化VO
        testVo = new BackGroundMusicVo();
        testVo.setId(1L);
        testVo.setTypeId(1L);
        testVo.setName("测试背景音乐");
        testVo.setMusicUrl("test/music.wav");
        testVo.setShopId(1L);
        testVo.setMusicTime(120);

        // 初始化实体
        testEntity = new BackgroundMusic();
        testEntity.setId(1L);
        testEntity.setTypeId(1L);
        testEntity.setName("测试背景音乐");
        testEntity.setMusicUrl("test/music.wav");
        testEntity.setDelStatus(0);
        testEntity.setCreateTime(new Date());
        testEntity.setShopId(1L);

        // 初始化类型
        testType = new BackgroundMusicType();
        testType.setId(1L);
        testType.setName("测试类型");

        // 初始化用户
        testUser = new SysUserVo();
        testUser.setId(1L);
        testUser.setUserName("测试用户");
        testUser.setLoginName("testuser");

        testLoginUser = new LoginUser();
        testLoginUser.setUser(testUser);
    }

    // ==================== list方法测试 ====================

    @Test
    @DisplayName("测试list方法 - 正常查询返回数据")
    void testList_Success_WithData() {
        // Given - 准备测试数据，不设置搜索条件避免参数不匹配
        BackgroundMusicDto simpleDto = new BackgroundMusicDto();
        simpleDto.setPageNumber(0);
        simpleDto.setPageSize(10);
        simpleDto.setShopId(1L);
        // 不设置searchCondition

        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(1L, 2L));

        // 模拟查询总数 - 使用更宽松的参数匹配
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(1L);

        // 模拟查询结果
        List<BackGroundMusicVo> mockResults = Arrays.asList(testVo);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(mockResults);

        // When - 执行测试
        Result result = backgroundMusicService.list(simpleDto);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应该返回成功状态码");

        // 验证数据内容
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "数据不应为空");
        assertEquals(1L, data.get("total"), "总数应该为1");
        assertEquals(mockResults, data.get("result"), "结果列表应该匹配");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }

    @Test
    @DisplayName("测试list方法 - 微信用户需要过滤时返回空结果")
    void testList_WeChatUserFilter_ReturnsEmpty() {
        // Given - 微信用户需要过滤
        when(shopRefUtil.isNeedWxFilter()).thenReturn(true);

        // When - 执行测试
        Result result = backgroundMusicService.list(testDto);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应该返回成功状态码");

        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "数据不应为空");
        assertEquals(0, data.get("total"), "总数应该为0");
        assertTrue(((List<?>) data.get("result")).isEmpty(), "结果列表应该为空");

        // 验证只调用了过滤检查，没有执行数据库查询
        verify(shopRefUtil).isNeedWxFilter();
        verify(jdbcTemplate, never()).queryForObject(anyString(), eq(Long.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试list方法 - 查询结果为空")
    void testList_EmptyResult() {
        // Given - 准备测试数据，不设置搜索条件
        BackgroundMusicDto simpleDto = new BackgroundMusicDto();
        simpleDto.setPageNumber(0);
        simpleDto.setPageSize(10);
        simpleDto.setShopId(1L);

        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(1L, 2L));

        // 模拟查询总数为0
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(0L);

        // When - 执行测试
        Result result = backgroundMusicService.list(simpleDto);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应该返回成功状态码");

        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data, "数据不应为空");
        assertEquals(0, data.get("total"), "总数应该为0");
        assertTrue(((List<?>) data.get("result")).isEmpty(), "结果列表应该为空");

        // 验证没有执行详细查询
        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Long.class), any());
        verify(jdbcTemplate, never()).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }

    @Test
    @DisplayName("测试list方法 - 带搜索条件查询")
    void testList_WithSearchCondition() {
        // Given - 准备带搜索条件的DTO
        BackgroundMusicDto dtoWithSearch = new BackgroundMusicDto();
        dtoWithSearch.setPageNumber(0);
        dtoWithSearch.setPageSize(10);
        dtoWithSearch.setSearchCondition("测试音乐");
        dtoWithSearch.setShopId(1L);

        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(1L));
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(1L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(Arrays.asList(testVo));

        // When - 执行测试
        Result result = backgroundMusicService.list(dtoWithSearch);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应该返回成功状态码");

        // 验证SQL参数包含搜索条件
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }

    @Test
    @DisplayName("测试list方法 - DTO为null时的处理")
    void testList_NullDto() {
        // Given - DTO为null，只设置必要的mock
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(1L));
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(1L);

        // When - 执行测试，但是会因为null dto导致AssertionError
        // 实际上service方法在dto为null时会在第96行assert dto != null处抛出AssertionError
        assertThrows(AssertionError.class, () -> {
            backgroundMusicService.list(null);
        }, "DTO为null时应该抛出AssertionError");

        // 验证方法调用
        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any());
    }

    // ==================== delete方法测试 ====================

    @Test
    @DisplayName("测试delete方法 - 正常删除成功")
    void testDelete_Success() {
        // Given - 准备测试数据
        BackgroundMusicDto deleteDto = new BackgroundMusicDto();
        deleteDto.setBackgroundMusicId(1L);

        // 模拟删除操作返回成功
        when(backgroundMusicRepository.updateDelStatusById(1L)).thenReturn(1);

        // When - 执行测试
        Result result = backgroundMusicService.delete(deleteDto);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应该返回成功状态码");
        // 注意：实际的delete方法只返回Result.success()，没有自定义消息

        // 验证方法调用
        verify(backgroundMusicRepository).updateDelStatusById(1L);
    }

    @Test
    @DisplayName("测试delete方法 - DTO为null时返回失败")
    void testDelete_NullDto_ReturnsFail() {
        // When - 执行测试，传入null
        Result result = backgroundMusicService.delete(null);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应该返回失败状态码");
        assertEquals("数据不能为空", result.getMessage(), "应该返回正确的错误消息");

        // 验证没有调用删除方法
        verify(backgroundMusicRepository, never()).updateDelStatusById(anyLong());
    }

    @Test
    @DisplayName("测试delete方法 - 背景音乐ID为null时返回失败")
    void testDelete_NullBackgroundMusicId_ReturnsFail() {
        // Given - 准备没有ID的DTO
        BackgroundMusicDto deleteDto = new BackgroundMusicDto();
        deleteDto.setBackgroundMusicId(null);

        // When - 执行测试
        Result result = backgroundMusicService.delete(deleteDto);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应该返回失败状态码");
        assertEquals("背景音乐id不能为空", result.getMessage(), "应该返回正确的错误消息");

        // 验证没有调用删除方法
        verify(backgroundMusicRepository, never()).updateDelStatusById(anyLong());
    }

    @Test
    @DisplayName("测试delete方法 - 删除不存在的记录")
    void testDelete_NonExistentRecord() {
        // Given - 准备测试数据
        BackgroundMusicDto deleteDto = new BackgroundMusicDto();
        deleteDto.setBackgroundMusicId(999L);

        // 模拟删除操作返回0（没有记录被删除）
        when(backgroundMusicRepository.updateDelStatusById(999L)).thenReturn(0);

        // When - 执行测试
        Result result = backgroundMusicService.delete(deleteDto);

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "即使没有记录被删除，也应该返回成功状态码");

        // 验证方法调用
        verify(backgroundMusicRepository).updateDelStatusById(999L);
    }

    @Test
    @DisplayName("测试delete方法 - 数据库异常处理")
    void testDelete_DatabaseException() {
        // Given - 准备测试数据
        BackgroundMusicDto deleteDto = new BackgroundMusicDto();
        deleteDto.setBackgroundMusicId(1L);

        // 模拟数据库异常
        when(backgroundMusicRepository.updateDelStatusById(1L))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // When & Then - 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            backgroundMusicService.delete(deleteDto);
        }, "应该抛出数据库异常");

        // 验证方法调用
        verify(backgroundMusicRepository).updateDelStatusById(1L);
    }

    // ==================== add方法测试 ====================

    @Test
    @DisplayName("测试add方法 - 正常添加成功")
    void testAdd_Success() {
        // Given - 准备测试数据
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class);
                MockedStatic<WavFileParsingUtils> wavUtilsMock = mockStatic(WavFileParsingUtils.class);
                MockedStatic<VideoUtil> videoUtilsMock = mockStatic(VideoUtil.class)) {

            // 模拟用户认证
            securityUtilsMock.when(SecurityUtils::getLoginUser).thenReturn(testLoginUser);

            // 创建真实的临时文件用于测试
            try {
                final File tempFile = File.createTempFile("test", ".wav");
                tempFile.deleteOnExit();

                // 模拟文件下载
                when(ossService.getObjectFile(null, "test/music.wav")).thenReturn(tempFile);

                // 模拟WAV文件解析 - 正确的格式
                byte[] mockBytes = new byte[] { 1, 0 }; // 模拟字节数据
                wavUtilsMock.when(() -> WavFileParsingUtils.read(any(RandomAccessFile.class), eq(22), eq(2)))
                        .thenReturn(mockBytes);
                wavUtilsMock.when(() -> WavFileParsingUtils.toShort(mockBytes)).thenReturn((short) 1); // 单声道

                byte[] mockBytes2 = new byte[] { 64, 62, 0, 0 }; // 模拟采样率字节
                wavUtilsMock.when(() -> WavFileParsingUtils.read(any(RandomAccessFile.class), eq(24), eq(4)))
                        .thenReturn(mockBytes2);
                wavUtilsMock.when(() -> WavFileParsingUtils.toInt(mockBytes2)).thenReturn(16000); // 16KHz

                byte[] mockBytes3 = new byte[] { 16, 0 }; // 模拟位深度字节
                wavUtilsMock.when(() -> WavFileParsingUtils.read(any(RandomAccessFile.class), eq(34), eq(2)))
                        .thenReturn(mockBytes3);
                wavUtilsMock.when(() -> WavFileParsingUtils.toShort(mockBytes3)).thenReturn((short) 16); // 16位

                // 模拟视频时长获取
                videoUtilsMock.when(() -> VideoUtil.getDuration(tempFile.getPath())).thenReturn(120L);

                // 模拟保存操作
                when(backgroundMusicRepository.save(any(BackgroundMusic.class))).thenReturn(testEntity);

                // When - 执行测试
                Result result = backgroundMusicService.add(testVo);

                // Then - 验证结果
                assertNotNull(result, "返回结果不应为空");
                assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应该返回成功状态码");

                // 验证方法调用
                verify(ossService).getObjectFile(null, "test/music.wav");
                verify(backgroundMusicRepository).save(any(BackgroundMusic.class));

            } catch (IOException e) {
                fail("创建临时文件失败: " + e.getMessage());
            }
        }
    }

    @Test
    @DisplayName("测试add方法 - 类型ID为null时返回失败")
    void testAdd_NullTypeId_ReturnsFail() {
        // Given - 准备没有类型ID的VO
        BackGroundMusicVo voWithoutTypeId = new BackGroundMusicVo();
        voWithoutTypeId.setTypeId(null);
        voWithoutTypeId.setName("测试音乐");
        voWithoutTypeId.setMusicUrl("test/music.wav");

        // Mock SecurityUtils
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            securityUtilsMock.when(SecurityUtils::getLoginUser).thenReturn(testLoginUser);

            // When - 执行测试
            Result result = backgroundMusicService.add(voWithoutTypeId);

            // Then - 验证结果
            assertNotNull(result, "返回结果不应为空");
            assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应该返回失败状态码");
            assertEquals("背景音乐分类id不能为空", result.getMessage(), "应该返回正确的错误消息");

            // 验证没有调用保存方法
            verify(backgroundMusicRepository, never()).save(any(BackgroundMusic.class));
        }
    }

    @Test
    @DisplayName("测试add方法 - 名称为空时返回失败")
    void testAdd_BlankName_ReturnsFail() {
        // Given - 准备没有名称的VO
        BackGroundMusicVo voWithoutName = new BackGroundMusicVo();
        voWithoutName.setTypeId(1L);
        voWithoutName.setName("");
        voWithoutName.setMusicUrl("test/music.wav");

        // Mock SecurityUtils
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            securityUtilsMock.when(SecurityUtils::getLoginUser).thenReturn(testLoginUser);

            // When - 执行测试
            Result result = backgroundMusicService.add(voWithoutName);

            // Then - 验证结果
            assertNotNull(result, "返回结果不应为空");
            assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应该返回失败状态码");
            assertEquals("背景音乐名称不能为空", result.getMessage(), "应该返回正确的错误消息");

            // 验证没有调用保存方法
            verify(backgroundMusicRepository, never()).save(any(BackgroundMusic.class));
        }
    }

    @Test
    @DisplayName("测试add方法 - 音乐URL为空时返回失败")
    void testAdd_BlankMusicUrl_ReturnsFail() {
        // Given - 准备没有音乐URL的VO
        BackGroundMusicVo voWithoutUrl = new BackGroundMusicVo();
        voWithoutUrl.setTypeId(1L);
        voWithoutUrl.setName("测试音乐");
        voWithoutUrl.setMusicUrl("");

        // Mock SecurityUtils
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            securityUtilsMock.when(SecurityUtils::getLoginUser).thenReturn(testLoginUser);

            // When - 执行测试
            Result result = backgroundMusicService.add(voWithoutUrl);

            // Then - 验证结果
            assertNotNull(result, "返回结果不应为空");
            assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应该返回失败状态码");
            assertEquals("背景音乐文件不能为空", result.getMessage(), "应该返回正确的错误消息");

            // 验证没有调用保存方法
            verify(backgroundMusicRepository, never()).save(any(BackgroundMusic.class));
        }
    }

    @Test
    @DisplayName("测试add方法 - WAV文件声道数不正确时返回失败")
    void testAdd_InvalidChannels_ReturnsFail() {
        // Given - 准备测试数据
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class);
                MockedStatic<WavFileParsingUtils> wavUtilsMock = mockStatic(WavFileParsingUtils.class)) {

            securityUtilsMock.when(SecurityUtils::getLoginUser).thenReturn(testLoginUser);

            try {
                final File tempFile = File.createTempFile("test", ".wav");
                tempFile.deleteOnExit();
                when(ossService.getObjectFile(null, "test/music.wav")).thenReturn(tempFile);

                // 模拟双声道（不正确）
                byte[] mockBytes = new byte[] { 2, 0 };
                wavUtilsMock.when(() -> WavFileParsingUtils.read(any(RandomAccessFile.class), eq(22), eq(2)))
                        .thenReturn(mockBytes);
                wavUtilsMock.when(() -> WavFileParsingUtils.toShort(mockBytes)).thenReturn((short) 2);

                // When - 执行测试
                Result result = backgroundMusicService.add(testVo);

                // Then - 验证结果
                assertNotNull(result, "返回结果不应为空");
                assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应该返回失败状态码");
                assertTrue(result.getMessage().contains("必须单声道WAV格式"), "错误消息应该包含声道数错误信息");

            } catch (IOException e) {
                fail("创建临时文件失败: " + e.getMessage());
            }
        }
    }

    @Test
    @DisplayName("测试add方法 - WAV文件采样率不正确时返回失败")
    void testAdd_InvalidSampleRate_ReturnsFail() {
        // Given - 准备测试数据
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class);
                MockedStatic<WavFileParsingUtils> wavUtilsMock = mockStatic(WavFileParsingUtils.class)) {

            securityUtilsMock.when(SecurityUtils::getLoginUser).thenReturn(testLoginUser);

            try {
                final File tempFile = File.createTempFile("test", ".wav");
                tempFile.deleteOnExit();
                when(ossService.getObjectFile(null, "test/music.wav")).thenReturn(tempFile);

                // 模拟正确的声道数
                byte[] mockBytes1 = new byte[] { 1, 0 };
                wavUtilsMock.when(() -> WavFileParsingUtils.read(any(RandomAccessFile.class), eq(22), eq(2)))
                        .thenReturn(mockBytes1);
                wavUtilsMock.when(() -> WavFileParsingUtils.toShort(mockBytes1)).thenReturn((short) 1);

                // 模拟错误的采样率（8KHz，太低）
                byte[] mockBytes2 = new byte[] { 64, 31, 0, 0 };
                wavUtilsMock.when(() -> WavFileParsingUtils.read(any(RandomAccessFile.class), eq(24), eq(4)))
                        .thenReturn(mockBytes2);
                wavUtilsMock.when(() -> WavFileParsingUtils.toInt(mockBytes2)).thenReturn(8000);

                // When - 执行测试
                Result result = backgroundMusicService.add(testVo);

                // Then - 验证结果
                assertNotNull(result, "返回结果不应为空");
                assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应该返回失败状态码");
                assertTrue(result.getMessage().contains("采样率必须16 kHz"), "错误消息应该包含采样率错误信息");

            } catch (IOException e) {
                fail("创建临时文件失败: " + e.getMessage());
            }
        }
    }

    @Test
    @DisplayName("测试add方法 - WAV文件位深度不正确时返回失败")
    void testAdd_InvalidBitDepth_ReturnsFail() {
        // Given - 准备测试数据
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class);
                MockedStatic<WavFileParsingUtils> wavUtilsMock = mockStatic(WavFileParsingUtils.class)) {

            securityUtilsMock.when(SecurityUtils::getLoginUser).thenReturn(testLoginUser);

            try {
                final File tempFile = File.createTempFile("test", ".wav");
                tempFile.deleteOnExit();
                when(ossService.getObjectFile(null, "test/music.wav")).thenReturn(tempFile);

                // 模拟正确的声道数和采样率
                byte[] mockBytes1 = new byte[] { 1, 0 };
                wavUtilsMock.when(() -> WavFileParsingUtils.read(any(RandomAccessFile.class), eq(22), eq(2)))
                        .thenReturn(mockBytes1);
                wavUtilsMock.when(() -> WavFileParsingUtils.toShort(mockBytes1)).thenReturn((short) 1);

                byte[] mockBytes2 = new byte[] { 64, 62, 0, 0 };
                wavUtilsMock.when(() -> WavFileParsingUtils.read(any(RandomAccessFile.class), eq(24), eq(4)))
                        .thenReturn(mockBytes2);
                wavUtilsMock.when(() -> WavFileParsingUtils.toInt(mockBytes2)).thenReturn(16000);

                // 模拟错误的位深度（8位，不正确）
                byte[] mockBytes3 = new byte[] { 8, 0 };
                wavUtilsMock.when(() -> WavFileParsingUtils.read(any(RandomAccessFile.class), eq(34), eq(2)))
                        .thenReturn(mockBytes3);
                wavUtilsMock.when(() -> WavFileParsingUtils.toShort(mockBytes3)).thenReturn((short) 8);

                // When - 执行测试
                Result result = backgroundMusicService.add(testVo);

                // Then - 验证结果
                assertNotNull(result, "返回结果不应为空");
                assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应该返回失败状态码");
                assertTrue(result.getMessage().contains("位深度要求16位"), "错误消息应该包含位深度错误信息");

            } catch (IOException e) {
                fail("创建临时文件失败: " + e.getMessage());
            }
        }
    }

    @Test
    @DisplayName("测试add方法 - 文件解析异常时返回失败")
    void testAdd_FileParsingException_ReturnsFail() {
        // Given - 准备测试数据
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class);
                MockedStatic<WavFileParsingUtils> wavUtilsMock = mockStatic(WavFileParsingUtils.class)) {

            securityUtilsMock.when(SecurityUtils::getLoginUser).thenReturn(testLoginUser);

            try {
                final File tempFile = File.createTempFile("test", ".wav");
                tempFile.deleteOnExit();
                when(ossService.getObjectFile(null, "test/music.wav")).thenReturn(tempFile);

                // 模拟文件解析异常
                wavUtilsMock.when(() -> WavFileParsingUtils.read(any(RandomAccessFile.class), anyInt(), anyInt()))
                        .thenThrow(new RuntimeException("文件读取异常"));

                // When - 执行测试
                Result result = backgroundMusicService.add(testVo);

                // Then - 验证结果
                assertNotNull(result, "返回结果不应为空");
                assertEquals(ResultCode.FAIL.getCode(), result.getCode(), "应该返回失败状态码");
                assertEquals("背景音乐文件分析失败", result.getMessage(), "应该返回文件分析失败消息");

            } catch (IOException e) {
                fail("创建临时文件失败: " + e.getMessage());
            }
        }
    }

    // ==================== findBackgroundMusicType方法测试 ====================

    @Test
    @DisplayName("测试findBackgroundMusicType方法 - 正常获取类型列表")
    void testFindBackgroundMusicType_Success() {
        // Given - 准备测试数据
        List<BackgroundMusicType> mockTypes = Arrays.asList(testType);
        when(backgroundMusicTypeRepository.findAll()).thenReturn(mockTypes);

        // When - 执行测试
        Result result = backgroundMusicService.findBackgroundMusicType();

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应该返回成功状态码");
        assertEquals(mockTypes, result.getData(), "返回的数据应该匹配");

        // 验证方法调用
        verify(backgroundMusicTypeRepository).findAll();
    }

    @Test
    @DisplayName("测试findBackgroundMusicType方法 - 空列表")
    void testFindBackgroundMusicType_EmptyList() {
        // Given - 准备空列表
        List<BackgroundMusicType> emptyList = new ArrayList<>();
        when(backgroundMusicTypeRepository.findAll()).thenReturn(emptyList);

        // When - 执行测试
        Result result = backgroundMusicService.findBackgroundMusicType();

        // Then - 验证结果
        assertNotNull(result, "返回结果不应为空");
        assertEquals(ResultCode.SUCCESS.getCode(), result.getCode(), "应该返回成功状态码");
        assertEquals(emptyList, result.getData(), "返回的数据应该是空列表");

        // 验证方法调用
        verify(backgroundMusicTypeRepository).findAll();
    }

    @Test
    @DisplayName("测试findBackgroundMusicType方法 - 数据库异常")
    void testFindBackgroundMusicType_DatabaseException() {
        // Given - 模拟数据库异常
        when(backgroundMusicTypeRepository.findAll())
                .thenThrow(new RuntimeException("数据库连接异常"));

        // When & Then - 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            backgroundMusicService.findBackgroundMusicType();
        }, "应该抛出数据库异常");

        // 验证方法调用
        verify(backgroundMusicTypeRepository).findAll();
    }
}
